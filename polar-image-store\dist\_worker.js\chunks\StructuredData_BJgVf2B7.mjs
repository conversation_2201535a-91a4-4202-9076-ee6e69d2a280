globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,r as renderComponent,d as renderTemplate,m as maybeRenderHead,b as addAttribute,s as spreadAttributes,u as unescapeHTML}from"./astro/server_BgKLHZ62.mjs";import{$ as $$Image}from"./_astro_assets_BdkCObdS.mjs";import{getResponsiveImageUrls,getOptimizedImageUrl,generateSizesAttribute}from"./imageOptimization_Dac7PwDt.mjs";const $$Astro$1=createAstro("https://infpik.store"),$$OptimizedImage=createComponent((async(e,t,r)=>{const a=e.createAstro($$Astro$1,t,r);a.self=$$OptimizedImage;const{src:i,alt:s,width:o=800,height:n=600,quality:p=85,format:c="auto",fit:m="scale-down",loading:d="lazy",fetchpriority:g="auto",class:l="",style:u="",responsive:h=!0,sizes:f=[320,640,960,1280,1920],densities:$=[1,2],preset:y,...b}=a.props;let A,I;if(y){const{ImagePresets:e}=await import("./imageOptimization_Dac7PwDt.mjs");A=e[y](i)}else if(h){const e=getResponsiveImageUrls(i,{sizes:f,densities:$,width:o,height:n,quality:p,format:c,fit:m});A=e.src,I=e.srcset}else{A=getOptimizedImageUrl(i,{width:o,height:n,quality:p,format:c,fit:m})}const _=h?generateSizesAttribute():void 0,z=i.startsWith("/")||i.includes("placeholder");return renderTemplate`${z?renderTemplate`<!-- Use Astro's built-in Image component for local images -->
  ${renderComponent(e,"Image",$$Image,{src:i,alt:s,width:o,height:n,loading:d,fetchpriority:g,class:l,style:u,...b})}`:renderTemplate`<!-- Use optimized external image with Cloudflare Image Transform -->
  ${maybeRenderHead()}<img${addAttribute(A,"src")}${addAttribute(I,"srcset")}${addAttribute(_,"sizes")}${addAttribute(s,"alt")}${addAttribute(o,"width")}${addAttribute(n,"height")}${addAttribute(d,"loading")}${addAttribute(g,"fetchpriority")}${addAttribute(l,"class")}${addAttribute(u,"style")}${spreadAttributes(b)}>`}`}),"D:/code/image/polar-image-store/src/components/OptimizedImage.astro",void 0);var _a,__freeze=Object.freeze,__defProp=Object.defineProperty,__template=(e,t)=>__freeze(__defProp(e,"raw",{value:__freeze(e.slice())}));const $$Astro=createAstro("https://infpik.store"),$$StructuredData=createComponent(((e,t,r)=>{const a=e.createAstro($$Astro,t,r);a.self=$$StructuredData;const{type:i,data:s}=a.props;const o=function(e,t){const r={"@context":"https://schema.org","@type":e};switch(e){case"Product":return{...r,name:t.name,description:t.description,image:t.images||[],sku:t.id,brand:{"@type":"Brand",name:"InfPik"},offers:{"@type":"Offer",price:t.price,priceCurrency:t.currency||"USD",availability:t.isAvailable?"https://schema.org/InStock":"https://schema.org/OutOfStock",seller:{"@type":"Organization",name:"InfPik",url:"https://infpik.store"},url:t.url},category:"Digital Icons",productID:t.id,...t.aggregateRating&&{aggregateRating:{"@type":"AggregateRating",ratingValue:t.aggregateRating.ratingValue,reviewCount:t.aggregateRating.reviewCount}}};case"Organization":return{...r,name:"InfPik",url:"https://infpik.store",logo:"https://infpik.store/favicon.svg",description:"Premium 3D Premium Icons Store",sameAs:["https://twitter.com/polarimagestore","https://facebook.com/polarimagestore"],contactPoint:{"@type":"ContactPoint",contactType:"customer service",email:"<EMAIL>"}};case"WebSite":return{...r,name:"InfPik",url:"https://infpik.store",description:"3D Premium Icons Store",publisher:{"@type":"Organization",name:"InfPik"},potentialAction:{"@type":"SearchAction",target:"https://infpik.store/products?search={search_term_string}","query-input":"required name=search_term_string"}};case"BreadcrumbList":return{...r,itemListElement:t.items.map(((e,t)=>({"@type":"ListItem",position:t+1,name:e.name,item:e.url})))};case"FAQPage":return{...r,mainEntity:t.faqs.map((e=>({"@type":"Question",name:e.question,acceptedAnswer:{"@type":"Answer",text:e.answer}})))};case"Article":return{...r,headline:t.title,author:{"@type":"Person",name:t.author},datePublished:t.publishDate,dateModified:t.modifiedDate,publisher:{"@type":"Organization",name:"InfPik"},image:t.images?.map((e=>({"@type":"ImageObject",url:e,width:"1200",height:"900",caption:t.title})))||[]};default:return r}}(i,s);return renderTemplate(_a||(_a=__template(['<script type="application/ld+json">',"<\/script>"])),unescapeHTML(JSON.stringify(o)))}),"D:/code/image/polar-image-store/src/components/StructuredData.astro",void 0);export{$$OptimizedImage as $,$$StructuredData as a};