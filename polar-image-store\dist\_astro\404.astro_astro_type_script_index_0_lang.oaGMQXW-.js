document.addEventListener("DOMContentLoaded",()=>{const i=document.getElementById("error-page-search"),t=document.getElementById("errorPageSearchResults");let c;function a(){return window.innerWidth<768}function u(e){if(a()){e.blur();const r=e.value.trim();window.openSearchModal?.(r)}}function h(e){try{const s=JSON.parse(localStorage.getItem("recentSearches")||"[]").filter(n=>n.query!==e);s.unshift({query:e,timestamp:Date.now()}),localStorage.setItem("recentSearches",JSON.stringify(s.slice(0,10)))}catch(r){console.error("Failed to save recent search:",r)}}function l(){if(!t)return;const e=JSON.parse(localStorage.getItem("recentSearches")||"[]");if(e.length===0){t.classList.add("hidden");return}t.innerHTML=`
        <div class="border-b border-primary-100 bg-gray-50 px-3 py-2">
          <div class="flex items-center justify-between">
            <span class="text-xs text-primary-500 font-medium">Recent Searches</span>
            <button id="clearRecentSearches" class="text-xs text-primary-400 hover:text-primary-600 transition-colors">
              Clear
            </button>
          </div>
        </div>
        <div class="p-2">
           ${e.map(s=>`
             <div class="flex items-center justify-between p-2 hover:bg-primary-50 rounded-lg transition-colors group">
               <button class="recent-search-item flex-1 text-left text-primary-700 hover:text-primary-900 transition-colors" data-query="${s}">
                 <div class="flex items-center gap-2">
                   <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                   </svg>
                   <span class="text-sm">${s}</span>
                 </div>
               </button>
               <button class="delete-recent-search ml-2 p-1 text-primary-400 hover:text-red-500 transition-colors" data-query="${s}">
                 <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                   <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                 </svg>
               </button>
             </div>
           `).join("")}
        </div>
      `,t.classList.remove("hidden"),t.querySelectorAll(".recent-search-item").forEach(s=>{s.addEventListener("click",n=>{const o=n.currentTarget.dataset.query;i&&(i.value=o,v(o))})}),t.querySelectorAll(".delete-recent-search").forEach(s=>{s.addEventListener("click",n=>{n.stopPropagation();const o=n.currentTarget.dataset.query;let d=JSON.parse(localStorage.getItem("recentSearches")||"[]");d=d.filter(x=>x!==o),localStorage.setItem("recentSearches",JSON.stringify(d)),l()})}),t.querySelector("#clearRecentSearches")?.addEventListener("click",()=>{localStorage.removeItem("recentSearches"),t?.classList.add("hidden")})}async function v(e){if(t)try{const s=await(await fetch(`/api/search?q=${encodeURIComponent(e)}`)).json();s.results&&s.results.length>0?(h(e),m(s.results,e)):p(e)}catch(r){console.error("Search failed:",r),f()}}function m(e,r){if(!t)return;const s=e.slice(0,5).map(n=>`
        <button onclick="window.location.href='${n.url}'" class="block w-full p-3 hover:bg-primary-50 rounded-lg transition-colors border-b border-primary-100 last:border-b-0 text-left">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center">
                <svg class="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <div class="flex-1 min-w-0">
                <div class="text-primary-900 font-medium truncate">${n.displayName}</div>
                <div class="text-primary-600 text-sm">${n.count} ${n.count===1?"product":"products"}</div>
              </div>
            </div>
            <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </button>
      `).join("");t.innerHTML=`
        <div class="p-2">
          <div class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100">
            Search All icons for "${r}"
          </div>
          ${s}
          ${e.length>5?`
            <div class="p-3 border-t border-primary-100">
              <div class="text-center text-primary-600 text-sm">
                Showing ${e.length} tags
              </div>
            </div>
          `:""}
        </div>
      `,t.classList.remove("hidden")}function p(e){t&&(t.innerHTML=`
         <div class="p-4 text-center text-primary-600">
           <div class="text-sm">No tags found for "${e}"</div>
           <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm mt-2 inline-block">
             Browse all icons →
           </a>
         </div>
       `,t.classList.remove("hidden"))}function f(){t&&(t.innerHTML=`
        <div class="p-4 text-center text-red-600">
          <div class="text-sm">Search failed. Please try again.</div>
        </div>
      `,t.classList.remove("hidden"))}i&&(i.addEventListener("focus",e=>{a()?u(e.target):e.target.value.trim()||l()}),i.addEventListener("click",e=>{a()&&u(e.target)}),i.addEventListener("input",e=>{if(a())return;const r=e.target.value.trim();c&&clearTimeout(c),r.length>=2?c=setTimeout(()=>{v(r)},300):l()}),i.addEventListener("keydown",e=>{if(e.key==="Enter"){e.preventDefault();const r=e.target.value.trim();r&&(h(r),a()?window.openSearchModal?.(r):window.location.href=`/products?search=${encodeURIComponent(r)}`)}})),document.addEventListener("click",e=>{t&&!i?.contains(e.target)&&!t.contains(e.target)&&t.classList.add("hidden")})});
