globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,m as maybeRenderHead,r as renderComponent,e as renderScript,b as addAttribute,d as renderTemplate,u as unescapeHTML}from"../../chunks/astro/server_BgKLHZ62.mjs";import{$ as $$Layout}from"../../chunks/Layout_Nkpn6gse.mjs";import{$ as $$OptimizedImage,a as $$StructuredData}from"../../chunks/StructuredData_BJgVf2B7.mjs";import{c as createPolarClient,t as transformPolarProduct,j as formatPrice}from"../../chunks/polar_D7XkB6p_.mjs";export{renderers}from"../../renderers.mjs";const $$Astro$5=createAstro("https://infpik.store"),$$ImageGallery=createComponent(((e,t,r)=>{const n=e.createAstro($$Astro$5,t,r);n.self=$$ImageGallery;const{images:s,productName:o}=n.props,i=s.length>0?s:["/placeholder-image.svg"];return renderTemplate`${maybeRenderHead()}<div class="flex flex-col gap-4" data-astro-cid-gjhjmbi3> <div class="relative aspect-video rounded-2xl overflow-hidden bg-gray-50 cursor-zoom-in flex items-center justify-center group" data-astro-cid-gjhjmbi3> ${renderComponent(e,"OptimizedImage",$$OptimizedImage,{id:"mainImage",src:i[0],alt:o,preset:"productDetail",loading:"eager",fetchpriority:"high","data-lightbox-index":"0",class:"max-w-full max-h-full w-auto h-auto object-contain transition-transform duration-300 group-hover:scale-105","data-astro-cid-gjhjmbi3":!0})} </div> ${i.length>1&&renderTemplate`<div class="flex gap-4 overflow-x-auto pb-2 mt-6" data-astro-cid-gjhjmbi3> ${i.slice(1).map(((t,r)=>renderTemplate`<div class="flex-shrink-0 w-24 h-24 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200 hover:border-primary-500 hover:scale-105 border-gray-200"${addAttribute(r+1,"data-thumbnail-index")}${addAttribute(t,"data-image-src")}${addAttribute(r+1,"data-lightbox-index")} data-astro-cid-gjhjmbi3> ${renderComponent(e,"OptimizedImage",$$OptimizedImage,{src:t,alt:`${o} - Image ${r+2}`,preset:"thumbnail",loading:"eager",class:"w-full h-full object-cover","data-astro-cid-gjhjmbi3":!0})} </div>`))} </div>`} </div> <!-- Lightbox Modal --> <div id="lightbox" class="hidden fixed inset-0 z-[1000] bg-black/90 animate-fade-in" data-astro-cid-gjhjmbi3> <div class="relative max-w-[90%] max-h-[90%] flex items-center justify-center h-full mx-auto pt-20" data-astro-cid-gjhjmbi3> <button id="closeButton" class="absolute -top-12 right-0 text-white text-3xl font-bold bg-none border-none cursor-pointer z-[1001] hover:text-gray-300 transition-colors" data-astro-cid-gjhjmbi3>
&times;
</button> <button id="prevButton" class="absolute top-1/2 -translate-y-1/2 -left-16 text-white text-2xl font-bold bg-black/60 border-none cursor-pointer w-12 h-12 rounded-full transition-all duration-200 hover:bg-black/80 hover:scale-110 flex items-center justify-center" data-astro-cid-gjhjmbi3>
&#8249;
</button> ${renderComponent(e,"OptimizedImage",$$OptimizedImage,{id:"lightboxImage",src:"/placeholder-image.svg",alt:"Lightbox image",width:1200,height:800,loading:"lazy",class:"max-w-full max-h-full object-contain rounded-2xl","data-astro-cid-gjhjmbi3":!0})} <button id="nextButton" class="absolute top-1/2 -translate-y-1/2 -right-16 text-white text-2xl font-bold bg-black/60 border-none cursor-pointer w-12 h-12 rounded-full transition-all duration-200 hover:bg-black/80 hover:scale-110 flex items-center justify-center" data-astro-cid-gjhjmbi3>
&#8250;
</button> <div class="absolute -bottom-12 left-1/2 -translate-x-1/2 text-white bg-black/70 px-4 py-2 rounded-full" data-astro-cid-gjhjmbi3> <span id="imageCounter" data-astro-cid-gjhjmbi3>1 / 1</span> </div> </div> </div>  ${renderScript(e,"D:/code/image/polar-image-store/src/components/ImageGallery.astro?astro&type=script&index=0&lang.ts")}`}),"D:/code/image/polar-image-store/src/components/ImageGallery.astro",void 0),$$Astro$4=createAstro("https://infpik.store"),$$RelatedImages=createComponent((async(e,t,r)=>{const n=e.createAstro($$Astro$4,t,r);n.self=$$RelatedImages;const{currentProduct:s}=n.props;function o(e,t){if(!e.tags||!t.tags)return 0;const r=new Set(e.tags),n=new Set(t.tags);let s=0;for(const e of r)n.has(e)&&s++;return s}let i=[];try{const e=createPolarClient(),t="e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(t&&s.tags&&s.tags.length>0){const r=await e.products.list({organizationId:t,isArchived:!1}),n=r.result?.items||[],a=n.map(transformPolarProduct).filter((e=>null!==e)).filter((e=>e.id!==s.id&&e.tags&&e.tags.length>0));i=a.map((e=>({product:e,similarity:o(s,e)}))).filter((e=>e.similarity>0)).sort(((e,t)=>t.similarity-e.similarity)).slice(0,6).map((e=>e.product))}}catch(e){console.error("Error fetching related products:",e)}return renderTemplate`${i.length>0&&renderTemplate`${maybeRenderHead()}<section class="mt-16" data-astro-cid-4j3m3ndg><div class="mb-8" data-astro-cid-4j3m3ndg><h2 class="text-3xl font-bold text-primary-900 mb-4" data-astro-cid-4j3m3ndg>Related Icons</h2><p class="text-primary-600" data-astro-cid-4j3m3ndg>Discover similar icons you might like</p></div><div class="relative" data-astro-cid-4j3m3ndg><!-- Slider container --><div class="overflow-x-auto scrollbar-hide" data-astro-cid-4j3m3ndg><div class="flex gap-6 pb-4" style="width: max-content;" data-astro-cid-4j3m3ndg>${i.map((t=>renderTemplate`<div class="flex-none w-80" data-astro-cid-4j3m3ndg><div class="group bg-white rounded-2xl overflow-hidden shadow-sm border border-primary-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-xl hover:shadow-primary-500/10" data-astro-cid-4j3m3ndg>${t.images.length>0&&renderTemplate`<div class="relative aspect-[4/3] overflow-hidden bg-gradient-to-br from-primary-50 to-accent-50" data-astro-cid-4j3m3ndg>${renderComponent(e,"OptimizedImage",$$OptimizedImage,{src:t.images[0],alt:t.name,preset:"related",loading:"lazy",class:"w-full h-full object-cover transition-all duration-300 group-hover:scale-105","data-astro-cid-4j3m3ndg":!0})}<!-- Gradient overlay --><div class="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" data-astro-cid-4j3m3ndg></div><!-- Hover actions --><div class="absolute inset-0 flex items-center justify-center gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-4 group-hover:translate-y-0" data-astro-cid-4j3m3ndg><a${addAttribute(`/products/${t.slug}`,"href")} class="flex items-center gap-2 px-4 py-2 bg-white/95 backdrop-blur-sm text-primary-900 rounded-full font-medium text-sm transition-all duration-200 hover:bg-white hover:scale-105" data-astro-cid-4j3m3ndg><svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-4j3m3ndg><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" data-astro-cid-4j3m3ndg></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" data-astro-cid-4j3m3ndg></path></svg>
View
</a></div><!-- Price badge --><div class="absolute top-3 right-3 px-2 py-1 bg-white/95 backdrop-blur-sm rounded-full" data-astro-cid-4j3m3ndg><span class="text-sm font-bold text-primary-900" data-astro-cid-4j3m3ndg>${formatPrice(t.price,t.currency)}</span></div></div>`}<div class="p-4" data-astro-cid-4j3m3ndg><!-- Tags -->${t.tags&&t.tags.length>0&&renderTemplate`<div class="mb-2" data-astro-cid-4j3m3ndg><div class="flex flex-wrap gap-1" data-astro-cid-4j3m3ndg>${t.tags.slice(0,2).map((e=>renderTemplate`<span class="inline-flex items-center px-2 py-0.5 bg-accent-100 text-accent-700 text-xs font-medium rounded-full" data-astro-cid-4j3m3ndg>
#${e}</span>`))}${t.tags.length>2&&renderTemplate`<span class="inline-flex items-center px-2 py-0.5 bg-primary-100 text-primary-600 text-xs font-medium rounded-full" data-astro-cid-4j3m3ndg>
+${t.tags.length-2}</span>`}</div></div>`}<!-- Title --><h3 class="text-lg font-bold text-primary-900 mb-2 line-clamp-2 group-hover:text-accent-600 transition-colors" data-astro-cid-4j3m3ndg>${t.name}</h3><!-- Description --><p class="text-primary-600 text-sm line-clamp-2 leading-relaxed" data-astro-cid-4j3m3ndg>${t.description}</p></div></div></div>`))}</div></div><!-- Scroll indicators (optional) --><div class="flex justify-center mt-4 gap-2" data-astro-cid-4j3m3ndg>${i.map(((e,t)=>renderTemplate`<div class="w-2 h-2 bg-primary-200 rounded-full" data-astro-cid-4j3m3ndg></div>`))}</div></div></section>`}`}),"D:/code/image/polar-image-store/src/components/RelatedImages.astro",void 0),$$Astro$3=createAstro("https://infpik.store"),$$TrustSignals=createComponent(((e,t,r)=>{const n=e.createAstro($$Astro$3,t,r);n.self=$$TrustSignals;const{class:s=""}=n.props;return renderTemplate`${maybeRenderHead()}<div${addAttribute(`flex flex-wrap items-center gap-4 text-sm text-primary-600 ${s}`,"class")}> <div class="flex items-center gap-2"> <svg class="w-4 h-4 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path> </svg> <span class="font-medium">Secure Payment</span> </div> <div class="flex items-center gap-2"> <svg class="w-4 h-4 text-blue-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path> </svg> <span class="font-medium">Instant Download</span> </div> <div class="flex items-center gap-2"> <svg class="w-4 h-4 text-purple-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16l-3-9m3 9l3-9"></path> </svg> <span class="font-medium">Commercial License</span> </div> </div>`}),"D:/code/image/polar-image-store/src/components/TrustSignals.astro",void 0),$$Astro$2=createAstro("https://infpik.store"),$$ProductBenefits=createComponent(((e,t,r)=>{const n=e.createAstro($$Astro$2,t,r);n.self=$$ProductBenefits;const{class:s=""}=n.props;return renderTemplate`${maybeRenderHead()}<div${addAttribute(`bg-gradient-to-br from-accent-50 to-primary-50 rounded-2xl p-6 ${s}`,"class")}> <h3 class="text-lg font-semibold text-primary-900 mb-4 flex items-center gap-2"> <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path> </svg>
Why Choose Our 3D Premium Icons?
</h3> <div class="space-y-4"> <div class="flex items-start gap-3"> <div class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0 mt-0.5"> <svg class="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> </div> <div> <h4 class="font-medium text-primary-900">Professional Quality</h4> <p class="text-sm text-primary-600">High-resolution 3D icons for professional use</p> </div> </div> <div class="flex items-start gap-3"> <div class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-0.5"> <svg class="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> </div> <div> <h4 class="font-medium text-primary-900">Commercial License</h4> <p class="text-sm text-primary-600">Use for personal and commercial projects</p> </div> </div> <div class="flex items-start gap-3"> <div class="w-6 h-6 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0 mt-0.5"> <svg class="w-3 h-3 text-purple-600" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> </div> <div> <h4 class="font-medium text-primary-900">Multiple Formats</h4> <p class="text-sm text-primary-600">Available in PSD, PNG</p> </div> </div> <div class="flex items-start gap-3"> <div class="w-6 h-6 rounded-full bg-orange-100 flex items-center justify-center flex-shrink-0 mt-0.5"> <svg class="w-3 h-3 text-orange-600" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> </div> <div> <h4 class="font-medium text-primary-900">Update Regularly</h4> <p class="text-sm text-primary-600">You can get more what you buy</p> </div> </div> </div> </div>`}),"D:/code/image/polar-image-store/src/components/ProductBenefits.astro",void 0),$$Astro$1=createAstro("https://infpik.store"),$$ProductFAQ=createComponent(((e,t,r)=>{const n=e.createAstro($$Astro$1,t,r);n.self=$$ProductFAQ;const{class:s=""}=n.props;return renderTemplate`${maybeRenderHead()}<div${addAttribute(`bg-primary-50 rounded-2xl p-6 ${s}`,"class")} data-astro-cid-wcd6ad2o> <h3 class="text-lg font-semibold text-primary-900 mb-6 flex items-center gap-2" data-astro-cid-wcd6ad2o> <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-wcd6ad2o> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" data-astro-cid-wcd6ad2o></path> </svg>
Frequently Asked Questions
</h3> <div class="space-y-4" data-astro-cid-wcd6ad2o> ${[{id:1,question:"What formats are included with my purchase?",answer:"You'll receive high-resolution JPEG files optimized for web and print use. Additional formats like PNG may be available depending on the specific image."},{id:2,question:"Can I use these icons for commercial projects?",answer:"Yes! All our icons come with a commercial license that allows you to use them in both personal and commercial projects without additional fees."},{id:3,question:"How do I download my icons after purchase?",answer:"After completing your purchase, you'll receive an email with download links. You can also access your downloads from your account dashboard."},{id:4,question:"Are there any usage restrictions?",answer:"Our license allows broad usage rights. You cannot resell or redistribute the icons as-is, but you can use them in your creative projects and commercial work."}].map((e=>renderTemplate`<details class="group bg-white rounded-lg border border-primary-200 overflow-hidden" data-astro-cid-wcd6ad2o> <summary class="flex items-center justify-between p-4 cursor-pointer hover:bg-primary-50 transition-colors" data-astro-cid-wcd6ad2o> <h4 class="font-medium text-primary-900 pr-4" data-astro-cid-wcd6ad2o>${e.question}</h4> <svg class="w-5 h-5 text-primary-600 transform transition-transform group-open:rotate-180 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-wcd6ad2o> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" data-astro-cid-wcd6ad2o></path> </svg> </summary> <div class="px-4 pb-4" data-astro-cid-wcd6ad2o> <p class="text-primary-700 leading-relaxed" data-astro-cid-wcd6ad2o>${e.answer}</p> </div> </details>`))} </div> <div class="mt-6 pt-4 border-t border-primary-200 text-center" data-astro-cid-wcd6ad2o> <p class="text-sm text-primary-600 mb-3" data-astro-cid-wcd6ad2o>Still have questions?</p> <a href="mailto:<EMAIL>" class="inline-flex items-center gap-2 text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors" data-astro-cid-wcd6ad2o> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-wcd6ad2o> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" data-astro-cid-wcd6ad2o></path> </svg>
Contact Support
</a> </div> </div> `}),"D:/code/image/polar-image-store/src/components/ProductFAQ.astro",void 0),decodeCache={};function getDecodeCache(e){let t=decodeCache[e];if(t)return t;t=decodeCache[e]=[];for(let e=0;e<128;e++){const r=String.fromCharCode(e);t.push(r)}for(let r=0;r<e.length;r++){const n=e.charCodeAt(r);t[n]="%"+("0"+n.toString(16).toUpperCase()).slice(-2)}return t}function decode$1(e,t){"string"!=typeof t&&(t=decode$1.defaultChars);const r=getDecodeCache(t);return e.replace(/(%[a-f0-9]{2})+/gi,(function(e){let t="";for(let n=0,s=e.length;n<s;n+=3){const o=parseInt(e.slice(n+1,n+3),16);if(o<128)t+=r[o];else{if(192==(224&o)&&n+3<s){const r=parseInt(e.slice(n+4,n+6),16);if(128==(192&r)){const e=o<<6&1984|63&r;t+=e<128?"��":String.fromCharCode(e),n+=3;continue}}if(224==(240&o)&&n+6<s){const r=parseInt(e.slice(n+4,n+6),16),s=parseInt(e.slice(n+7,n+9),16);if(128==(192&r)&&128==(192&s)){const e=o<<12&61440|r<<6&4032|63&s;t+=e<2048||e>=55296&&e<=57343?"���":String.fromCharCode(e),n+=6;continue}}if(240==(248&o)&&n+9<s){const r=parseInt(e.slice(n+4,n+6),16),s=parseInt(e.slice(n+7,n+9),16),i=parseInt(e.slice(n+10,n+12),16);if(128==(192&r)&&128==(192&s)&&128==(192&i)){let e=o<<18&1835008|r<<12&258048|s<<6&4032|63&i;e<65536||e>1114111?t+="����":(e-=65536,t+=String.fromCharCode(55296+(e>>10),56320+(1023&e))),n+=9;continue}}t+="�"}}return t}))}decode$1.defaultChars=";/?:@&=+$,#",decode$1.componentChars="";const encodeCache={};function getEncodeCache(e){let t=encodeCache[e];if(t)return t;t=encodeCache[e]=[];for(let e=0;e<128;e++){const r=String.fromCharCode(e);/^[0-9a-z]$/i.test(r)?t.push(r):t.push("%"+("0"+e.toString(16).toUpperCase()).slice(-2))}for(let r=0;r<e.length;r++)t[e.charCodeAt(r)]=e[r];return t}function encode$1(e,t,r){"string"!=typeof t&&(r=t,t=encode$1.defaultChars),void 0===r&&(r=!0);const n=getEncodeCache(t);let s="";for(let t=0,o=e.length;t<o;t++){const i=e.charCodeAt(t);if(r&&37===i&&t+2<o&&/^[0-9a-f]{2}$/i.test(e.slice(t+1,t+3)))s+=e.slice(t,t+3),t+=2;else if(i<128)s+=n[i];else if(i>=55296&&i<=57343){if(i>=55296&&i<=56319&&t+1<o){const r=e.charCodeAt(t+1);if(r>=56320&&r<=57343){s+=encodeURIComponent(e[t]+e[t+1]),t++;continue}}s+="%EF%BF%BD"}else s+=encodeURIComponent(e[t])}return s}function format(e){let t="";return t+=e.protocol||"",t+=e.slashes?"//":"",t+=e.auth?e.auth+"@":"",e.hostname&&-1!==e.hostname.indexOf(":")?t+="["+e.hostname+"]":t+=e.hostname||"",t+=e.port?":"+e.port:"",t+=e.pathname||"",t+=e.search||"",t+=e.hash||"",t}function Url(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}encode$1.defaultChars=";/?:@&=+$,-_.!~*'()#",encode$1.componentChars="-_.!~*'()";const protocolPattern=/^([a-z0-9.+-]+:)/i,portPattern=/:[0-9]*$/,simplePathPattern=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,delims=["<",">",'"',"`"," ","\r","\n","\t"],unwise=["{","}","|","\\","^","`"].concat(delims),autoEscape=["'"].concat(unwise),nonHostChars=["%","/","?",";","#"].concat(autoEscape),hostEndingChars=["/","?","#"],hostnameMaxLen=255,hostnamePartPattern=/^[+a-z0-9A-Z_-]{0,63}$/,hostnamePartStart=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,hostlessProtocol={javascript:!0,"javascript:":!0},slashedProtocol={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function urlParse(e,t){if(e&&e instanceof Url)return e;const r=new Url;return r.parse(e,t),r}Url.prototype.parse=function(e,t){let r,n,s,o=e;if(o=o.trim(),!t&&1===e.split("#").length){const e=simplePathPattern.exec(o);if(e)return this.pathname=e[1],e[2]&&(this.search=e[2]),this}let i=protocolPattern.exec(o);if(i&&(i=i[0],r=i.toLowerCase(),this.protocol=i,o=o.substr(i.length)),(t||i||o.match(/^\/\/[^@\/]+@[^@\/]+/))&&(s="//"===o.substr(0,2),!s||i&&hostlessProtocol[i]||(o=o.substr(2),this.slashes=!0)),!hostlessProtocol[i]&&(s||i&&!slashedProtocol[i])){let e,t,r=-1;for(let e=0;e<hostEndingChars.length;e++)n=o.indexOf(hostEndingChars[e]),-1!==n&&(-1===r||n<r)&&(r=n);t=-1===r?o.lastIndexOf("@"):o.lastIndexOf("@",r),-1!==t&&(e=o.slice(0,t),o=o.slice(t+1),this.auth=e),r=-1;for(let e=0;e<nonHostChars.length;e++)n=o.indexOf(nonHostChars[e]),-1!==n&&(-1===r||n<r)&&(r=n);-1===r&&(r=o.length),":"===o[r-1]&&r--;const s=o.slice(0,r);o=o.slice(r),this.parseHost(s),this.hostname=this.hostname||"";const i="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!i){const e=this.hostname.split(/\./);for(let t=0,r=e.length;t<r;t++){const r=e[t];if(r&&!r.match(hostnamePartPattern)){let n="";for(let e=0,t=r.length;e<t;e++)r.charCodeAt(e)>127?n+="x":n+=r[e];if(!n.match(hostnamePartPattern)){const n=e.slice(0,t),s=e.slice(t+1),i=r.match(hostnamePartStart);i&&(n.push(i[1]),s.unshift(i[2])),s.length&&(o=s.join(".")+o),this.hostname=n.join(".");break}}}}this.hostname.length>255&&(this.hostname=""),i&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}const a=o.indexOf("#");-1!==a&&(this.hash=o.substr(a),o=o.slice(0,a));const c=o.indexOf("?");return-1!==c&&(this.search=o.substr(c),o=o.slice(0,c)),o&&(this.pathname=o),slashedProtocol[r]&&this.hostname&&!this.pathname&&(this.pathname=""),this},Url.prototype.parseHost=function(e){let t=portPattern.exec(e);t&&(t=t[0],":"!==t&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)};const mdurl=Object.freeze(Object.defineProperty({__proto__:null,decode:decode$1,encode:encode$1,format:format,parse:urlParse},Symbol.toStringTag,{value:"Module"})),Any=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,Cc=/[\0-\x1F\x7F-\x9F]/,regex$1=/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u0890\u0891\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD80D[\uDC30-\uDC3F]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/,P=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,regex=/[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u0888\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20C0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u31EF\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC2\uFD40-\uFD4F\uFDCF\uFDFC-\uFDFF\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD833[\uDF50-\uDFC3]|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDEA\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEDC-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF76\uDF7B-\uDFD9\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDE53\uDE60-\uDE6D\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC5\uDECE-\uDEDB\uDEE0-\uDEE8\uDEF0-\uDEF8\uDF00-\uDF92\uDF94-\uDFCA]/,Z=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,ucmicro=Object.freeze(Object.defineProperty({__proto__:null,Any:Any,Cc:Cc,Cf:regex$1,P:P,S:regex,Z:Z},Symbol.toStringTag,{value:"Module"})),htmlDecodeTree=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map((e=>e.charCodeAt(0)))),xmlDecodeTree=new Uint16Array("Ȁaglq\tɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map((e=>e.charCodeAt(0))));var _a;const decodeMap=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),fromCodePoint$1=null!==(_a=String.fromCodePoint)&&void 0!==_a?_a:function(e){let t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e),t};function replaceCodePoint(e){var t;return e>=55296&&e<=57343||e>1114111?65533:null!==(t=decodeMap.get(e))&&void 0!==t?t:e}var CharCodes;!function(e){e[e.NUM=35]="NUM",e[e.SEMI=59]="SEMI",e[e.EQUALS=61]="EQUALS",e[e.ZERO=48]="ZERO",e[e.NINE=57]="NINE",e[e.LOWER_A=97]="LOWER_A",e[e.LOWER_F=102]="LOWER_F",e[e.LOWER_X=120]="LOWER_X",e[e.LOWER_Z=122]="LOWER_Z",e[e.UPPER_A=65]="UPPER_A",e[e.UPPER_F=70]="UPPER_F",e[e.UPPER_Z=90]="UPPER_Z"}(CharCodes||(CharCodes={}));const TO_LOWER_BIT=32;var BinTrieFlags,EntityDecoderState,DecodingMode;function isNumber(e){return e>=CharCodes.ZERO&&e<=CharCodes.NINE}function isHexadecimalCharacter(e){return e>=CharCodes.UPPER_A&&e<=CharCodes.UPPER_F||e>=CharCodes.LOWER_A&&e<=CharCodes.LOWER_F}function isAsciiAlphaNumeric(e){return e>=CharCodes.UPPER_A&&e<=CharCodes.UPPER_Z||e>=CharCodes.LOWER_A&&e<=CharCodes.LOWER_Z||isNumber(e)}function isEntityInAttributeInvalidEnd(e){return e===CharCodes.EQUALS||isAsciiAlphaNumeric(e)}!function(e){e[e.VALUE_LENGTH=49152]="VALUE_LENGTH",e[e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",e[e.JUMP_TABLE=127]="JUMP_TABLE"}(BinTrieFlags||(BinTrieFlags={})),function(e){e[e.EntityStart=0]="EntityStart",e[e.NumericStart=1]="NumericStart",e[e.NumericDecimal=2]="NumericDecimal",e[e.NumericHex=3]="NumericHex",e[e.NamedEntity=4]="NamedEntity"}(EntityDecoderState||(EntityDecoderState={})),function(e){e[e.Legacy=0]="Legacy",e[e.Strict=1]="Strict",e[e.Attribute=2]="Attribute"}(DecodingMode||(DecodingMode={}));class EntityDecoder{constructor(e,t,r){this.decodeTree=e,this.emitCodePoint=t,this.errors=r,this.state=EntityDecoderState.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=DecodingMode.Strict}startEntity(e){this.decodeMode=e,this.state=EntityDecoderState.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case EntityDecoderState.EntityStart:return e.charCodeAt(t)===CharCodes.NUM?(this.state=EntityDecoderState.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=EntityDecoderState.NamedEntity,this.stateNamedEntity(e,t));case EntityDecoderState.NumericStart:return this.stateNumericStart(e,t);case EntityDecoderState.NumericDecimal:return this.stateNumericDecimal(e,t);case EntityDecoderState.NumericHex:return this.stateNumericHex(e,t);case EntityDecoderState.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===CharCodes.LOWER_X?(this.state=EntityDecoderState.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=EntityDecoderState.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,r,n){if(t!==r){const s=r-t;this.result=this.result*Math.pow(n,s)+parseInt(e.substr(t,s),n),this.consumed+=s}}stateNumericHex(e,t){const r=t;for(;t<e.length;){const n=e.charCodeAt(t);if(!isNumber(n)&&!isHexadecimalCharacter(n))return this.addToNumericResult(e,r,t,16),this.emitNumericEntity(n,3);t+=1}return this.addToNumericResult(e,r,t,16),-1}stateNumericDecimal(e,t){const r=t;for(;t<e.length;){const n=e.charCodeAt(t);if(!isNumber(n))return this.addToNumericResult(e,r,t,10),this.emitNumericEntity(n,2);t+=1}return this.addToNumericResult(e,r,t,10),-1}emitNumericEntity(e,t){var r;if(this.consumed<=t)return null===(r=this.errors)||void 0===r||r.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===CharCodes.SEMI)this.consumed+=1;else if(this.decodeMode===DecodingMode.Strict)return 0;return this.emitCodePoint(replaceCodePoint(this.result),this.consumed),this.errors&&(e!==CharCodes.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){const{decodeTree:r}=this;let n=r[this.treeIndex],s=(n&BinTrieFlags.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){const o=e.charCodeAt(t);if(this.treeIndex=determineBranch(r,n,this.treeIndex+Math.max(1,s),o),this.treeIndex<0)return 0===this.result||this.decodeMode===DecodingMode.Attribute&&(0===s||isEntityInAttributeInvalidEnd(o))?0:this.emitNotTerminatedNamedEntity();if(n=r[this.treeIndex],s=(n&BinTrieFlags.VALUE_LENGTH)>>14,0!==s){if(o===CharCodes.SEMI)return this.emitNamedEntityData(this.treeIndex,s,this.consumed+this.excess);this.decodeMode!==DecodingMode.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var e;const{result:t,decodeTree:r}=this,n=(r[t]&BinTrieFlags.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,n,this.consumed),null===(e=this.errors)||void 0===e||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,r){const{decodeTree:n}=this;return this.emitCodePoint(1===t?n[e]&~BinTrieFlags.VALUE_LENGTH:n[e+1],r),3===t&&this.emitCodePoint(n[e+2],r),r}end(){var e;switch(this.state){case EntityDecoderState.NamedEntity:return 0===this.result||this.decodeMode===DecodingMode.Attribute&&this.result!==this.treeIndex?0:this.emitNotTerminatedNamedEntity();case EntityDecoderState.NumericDecimal:return this.emitNumericEntity(0,2);case EntityDecoderState.NumericHex:return this.emitNumericEntity(0,3);case EntityDecoderState.NumericStart:return null===(e=this.errors)||void 0===e||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case EntityDecoderState.EntityStart:return 0}}}function getDecoder(e){let t="";const r=new EntityDecoder(e,(e=>t+=fromCodePoint$1(e)));return function(e,n){let s=0,o=0;for(;(o=e.indexOf("&",o))>=0;){t+=e.slice(s,o),r.startEntity(n);const i=r.write(e,o+1);if(i<0){s=o+r.end();break}s=o+i,o=0===i?s+1:s}const i=t+e.slice(s);return t="",i}}function determineBranch(e,t,r,n){const s=(t&BinTrieFlags.BRANCH_LENGTH)>>7,o=t&BinTrieFlags.JUMP_TABLE;if(0===s)return 0!==o&&n===o?r:-1;if(o){const t=n-o;return t<0||t>=s?-1:e[r+t]-1}let i=r,a=i+s-1;for(;i<=a;){const t=i+a>>>1,r=e[t];if(r<n)i=t+1;else{if(!(r>n))return e[t+s];a=t-1}}return-1}const htmlDecoder=getDecoder(htmlDecodeTree);function decodeHTML(e,t=DecodingMode.Legacy){return htmlDecoder(e,t)}function _class$1(e){return Object.prototype.toString.call(e)}function isString$1(e){return"[object String]"===_class$1(e)}getDecoder(xmlDecodeTree);const _hasOwnProperty=Object.prototype.hasOwnProperty;function has(e,t){return _hasOwnProperty.call(e,t)}function assign$1(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){if(t){if("object"!=typeof t)throw new TypeError(t+"must be object");Object.keys(t).forEach((function(r){e[r]=t[r]}))}})),e}function arrayReplaceAt(e,t,r){return[].concat(e.slice(0,t),r,e.slice(t+1))}function isValidEntityCode(e){return!(e>=55296&&e<=57343)&&(!(e>=64976&&e<=65007)&&(!!(65535&~e&&65534!=(65535&e))&&(!(e>=0&&e<=8)&&(11!==e&&(!(e>=14&&e<=31)&&(!(e>=127&&e<=159)&&!(e>1114111)))))))}function fromCodePoint(e){if(e>65535){const t=55296+((e-=65536)>>10),r=56320+(1023&e);return String.fromCharCode(t,r)}return String.fromCharCode(e)}const UNESCAPE_MD_RE=/\\([!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])/g,ENTITY_RE=/&([a-z#][a-z0-9]{1,31});/gi,UNESCAPE_ALL_RE=new RegExp(UNESCAPE_MD_RE.source+"|"+ENTITY_RE.source,"gi"),DIGITAL_ENTITY_TEST_RE=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i;function replaceEntityPattern(e,t){if(35===t.charCodeAt(0)&&DIGITAL_ENTITY_TEST_RE.test(t)){const r="x"===t[1].toLowerCase()?parseInt(t.slice(2),16):parseInt(t.slice(1),10);return isValidEntityCode(r)?fromCodePoint(r):e}const r=decodeHTML(e);return r!==e?r:e}function unescapeMd(e){return e.indexOf("\\")<0?e:e.replace(UNESCAPE_MD_RE,"$1")}function unescapeAll(e){return e.indexOf("\\")<0&&e.indexOf("&")<0?e:e.replace(UNESCAPE_ALL_RE,(function(e,t,r){return t||replaceEntityPattern(e,r)}))}const HTML_ESCAPE_TEST_RE=/[&<>"]/,HTML_ESCAPE_REPLACE_RE=/[&<>"]/g,HTML_REPLACEMENTS={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function replaceUnsafeChar(e){return HTML_REPLACEMENTS[e]}function escapeHtml(e){return HTML_ESCAPE_TEST_RE.test(e)?e.replace(HTML_ESCAPE_REPLACE_RE,replaceUnsafeChar):e}const REGEXP_ESCAPE_RE=/[.?*+^$[\]\\(){}|-]/g;function escapeRE$1(e){return e.replace(REGEXP_ESCAPE_RE,"\\$&")}function isSpace(e){switch(e){case 9:case 32:return!0}return!1}function isWhiteSpace(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1}function isPunctChar(e){return P.test(e)||regex.test(e)}function isMdAsciiPunct(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}}function normalizeReference(e){return e=e.trim().replace(/\s+/g," "),"Ṿ"==="ẞ".toLowerCase()&&(e=e.replace(/ẞ/g,"ß")),e.toLowerCase().toUpperCase()}const lib={mdurl:mdurl,ucmicro:ucmicro},utils=Object.freeze(Object.defineProperty({__proto__:null,arrayReplaceAt:arrayReplaceAt,assign:assign$1,escapeHtml:escapeHtml,escapeRE:escapeRE$1,fromCodePoint:fromCodePoint,has:has,isMdAsciiPunct:isMdAsciiPunct,isPunctChar:isPunctChar,isSpace:isSpace,isString:isString$1,isValidEntityCode:isValidEntityCode,isWhiteSpace:isWhiteSpace,lib:lib,normalizeReference:normalizeReference,unescapeAll:unescapeAll,unescapeMd:unescapeMd},Symbol.toStringTag,{value:"Module"}));function parseLinkLabel(e,t,r){let n,s,o,i;const a=e.posMax,c=e.pos;for(e.pos=t+1,n=1;e.pos<a;){if(o=e.src.charCodeAt(e.pos),93===o&&(n--,0===n)){s=!0;break}if(i=e.pos,e.md.inline.skipToken(e),91===o)if(i===e.pos-1)n++;else if(r)return e.pos=c,-1}let l=-1;return s&&(l=e.pos),e.pos=c,l}function parseLinkDestination(e,t,r){let n,s=t;const o={ok:!1,pos:0,str:""};if(60===e.charCodeAt(s)){for(s++;s<r;){if(n=e.charCodeAt(s),10===n)return o;if(60===n)return o;if(62===n)return o.pos=s+1,o.str=unescapeAll(e.slice(t+1,s)),o.ok=!0,o;92===n&&s+1<r?s+=2:s++}return o}let i=0;for(;s<r&&(n=e.charCodeAt(s),32!==n)&&!(n<32||127===n);)if(92===n&&s+1<r){if(32===e.charCodeAt(s+1))break;s+=2}else{if(40===n&&(i++,i>32))return o;if(41===n){if(0===i)break;i--}s++}return t===s||0!==i||(o.str=unescapeAll(e.slice(t,s)),o.pos=s,o.ok=!0),o}function parseLinkTitle(e,t,r,n){let s,o=t;const i={ok:!1,can_continue:!1,pos:0,str:"",marker:0};if(n)i.str=n.str,i.marker=n.marker;else{if(o>=r)return i;let n=e.charCodeAt(o);if(34!==n&&39!==n&&40!==n)return i;t++,o++,40===n&&(n=41),i.marker=n}for(;o<r;){if(s=e.charCodeAt(o),s===i.marker)return i.pos=o+1,i.str+=unescapeAll(e.slice(t,o)),i.ok=!0,i;if(40===s&&41===i.marker)return i;92===s&&o+1<r&&o++,o++}return i.can_continue=!0,i.str+=unescapeAll(e.slice(t,o)),i}const helpers=Object.freeze(Object.defineProperty({__proto__:null,parseLinkDestination:parseLinkDestination,parseLinkLabel:parseLinkLabel,parseLinkTitle:parseLinkTitle},Symbol.toStringTag,{value:"Module"})),default_rules={};function Renderer(){this.rules=assign$1({},default_rules)}function Ruler(){this.__rules__=[],this.__cache__=null}function Token(e,t,r){this.type=e,this.tag=t,this.attrs=null,this.map=null,this.nesting=r,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}function StateCore(e,t,r){this.src=e,this.env=r,this.tokens=[],this.inlineMode=!1,this.md=t}default_rules.code_inline=function(e,t,r,n,s){const o=e[t];return"<code"+s.renderAttrs(o)+">"+escapeHtml(o.content)+"</code>"},default_rules.code_block=function(e,t,r,n,s){const o=e[t];return"<pre"+s.renderAttrs(o)+"><code>"+escapeHtml(e[t].content)+"</code></pre>\n"},default_rules.fence=function(e,t,r,n,s){const o=e[t],i=o.info?unescapeAll(o.info).trim():"";let a,c="",l="";if(i){const e=i.split(/(\s+)/g);c=e[0],l=e.slice(2).join("")}if(a=r.highlight&&r.highlight(o.content,c,l)||escapeHtml(o.content),0===a.indexOf("<pre"))return a+"\n";if(i){const e=o.attrIndex("class"),t=o.attrs?o.attrs.slice():[];e<0?t.push(["class",r.langPrefix+c]):(t[e]=t[e].slice(),t[e][1]+=" "+r.langPrefix+c);const n={attrs:t};return`<pre><code${s.renderAttrs(n)}>${a}</code></pre>\n`}return`<pre><code${s.renderAttrs(o)}>${a}</code></pre>\n`},default_rules.image=function(e,t,r,n,s){const o=e[t];return o.attrs[o.attrIndex("alt")][1]=s.renderInlineAsText(o.children,r,n),s.renderToken(e,t,r)},default_rules.hardbreak=function(e,t,r){return r.xhtmlOut?"<br />\n":"<br>\n"},default_rules.softbreak=function(e,t,r){return r.breaks?r.xhtmlOut?"<br />\n":"<br>\n":"\n"},default_rules.text=function(e,t){return escapeHtml(e[t].content)},default_rules.html_block=function(e,t){return e[t].content},default_rules.html_inline=function(e,t){return e[t].content},Renderer.prototype.renderAttrs=function(e){let t,r,n;if(!e.attrs)return"";for(n="",t=0,r=e.attrs.length;t<r;t++)n+=" "+escapeHtml(e.attrs[t][0])+'="'+escapeHtml(e.attrs[t][1])+'"';return n},Renderer.prototype.renderToken=function(e,t,r){const n=e[t];let s="";if(n.hidden)return"";n.block&&-1!==n.nesting&&t&&e[t-1].hidden&&(s+="\n"),s+=(-1===n.nesting?"</":"<")+n.tag,s+=this.renderAttrs(n),0===n.nesting&&r.xhtmlOut&&(s+=" /");let o=!1;if(n.block&&(o=!0,1===n.nesting&&t+1<e.length)){const r=e[t+1];("inline"===r.type||r.hidden||-1===r.nesting&&r.tag===n.tag)&&(o=!1)}return s+=o?">\n":">",s},Renderer.prototype.renderInline=function(e,t,r){let n="";const s=this.rules;for(let o=0,i=e.length;o<i;o++){const i=e[o].type;void 0!==s[i]?n+=s[i](e,o,t,r,this):n+=this.renderToken(e,o,t)}return n},Renderer.prototype.renderInlineAsText=function(e,t,r){let n="";for(let s=0,o=e.length;s<o;s++)switch(e[s].type){case"text":case"html_inline":case"html_block":n+=e[s].content;break;case"image":n+=this.renderInlineAsText(e[s].children,t,r);break;case"softbreak":case"hardbreak":n+="\n"}return n},Renderer.prototype.render=function(e,t,r){let n="";const s=this.rules;for(let o=0,i=e.length;o<i;o++){const i=e[o].type;"inline"===i?n+=this.renderInline(e[o].children,t,r):void 0!==s[i]?n+=s[i](e,o,t,r,this):n+=this.renderToken(e,o,t,r)}return n},Ruler.prototype.__find__=function(e){for(let t=0;t<this.__rules__.length;t++)if(this.__rules__[t].name===e)return t;return-1},Ruler.prototype.__compile__=function(){const e=this,t=[""];e.__rules__.forEach((function(e){e.enabled&&e.alt.forEach((function(e){t.indexOf(e)<0&&t.push(e)}))})),e.__cache__={},t.forEach((function(t){e.__cache__[t]=[],e.__rules__.forEach((function(r){r.enabled&&(t&&r.alt.indexOf(t)<0||e.__cache__[t].push(r.fn))}))}))},Ruler.prototype.at=function(e,t,r){const n=this.__find__(e),s=r||{};if(-1===n)throw new Error("Parser rule not found: "+e);this.__rules__[n].fn=t,this.__rules__[n].alt=s.alt||[],this.__cache__=null},Ruler.prototype.before=function(e,t,r,n){const s=this.__find__(e),o=n||{};if(-1===s)throw new Error("Parser rule not found: "+e);this.__rules__.splice(s,0,{name:t,enabled:!0,fn:r,alt:o.alt||[]}),this.__cache__=null},Ruler.prototype.after=function(e,t,r,n){const s=this.__find__(e),o=n||{};if(-1===s)throw new Error("Parser rule not found: "+e);this.__rules__.splice(s+1,0,{name:t,enabled:!0,fn:r,alt:o.alt||[]}),this.__cache__=null},Ruler.prototype.push=function(e,t,r){const n=r||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:n.alt||[]}),this.__cache__=null},Ruler.prototype.enable=function(e,t){Array.isArray(e)||(e=[e]);const r=[];return e.forEach((function(e){const n=this.__find__(e);if(n<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[n].enabled=!0,r.push(e)}),this),this.__cache__=null,r},Ruler.prototype.enableOnly=function(e,t){Array.isArray(e)||(e=[e]),this.__rules__.forEach((function(e){e.enabled=!1})),this.enable(e,t)},Ruler.prototype.disable=function(e,t){Array.isArray(e)||(e=[e]);const r=[];return e.forEach((function(e){const n=this.__find__(e);if(n<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[n].enabled=!1,r.push(e)}),this),this.__cache__=null,r},Ruler.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]},Token.prototype.attrIndex=function(e){if(!this.attrs)return-1;const t=this.attrs;for(let r=0,n=t.length;r<n;r++)if(t[r][0]===e)return r;return-1},Token.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]},Token.prototype.attrSet=function(e,t){const r=this.attrIndex(e),n=[e,t];r<0?this.attrPush(n):this.attrs[r]=n},Token.prototype.attrGet=function(e){const t=this.attrIndex(e);let r=null;return t>=0&&(r=this.attrs[t][1]),r},Token.prototype.attrJoin=function(e,t){const r=this.attrIndex(e);r<0?this.attrPush([e,t]):this.attrs[r][1]=this.attrs[r][1]+" "+t},StateCore.prototype.Token=Token;const NEWLINES_RE=/\r\n?|\n/g,NULL_RE=/\0/g;function normalize(e){let t;t=e.src.replace(NEWLINES_RE,"\n"),t=t.replace(NULL_RE,"�"),e.src=t}function block(e){let t;e.inlineMode?(t=new e.Token("inline","",0),t.content=e.src,t.map=[0,1],t.children=[],e.tokens.push(t)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}function inline(e){const t=e.tokens;for(let r=0,n=t.length;r<n;r++){const n=t[r];"inline"===n.type&&e.md.inline.parse(n.content,e.md,e.env,n.children)}}function isLinkOpen$1(e){return/^<a[>\s]/i.test(e)}function isLinkClose$1(e){return/^<\/a\s*>/i.test(e)}function linkify$1(e){const t=e.tokens;if(e.md.options.linkify)for(let r=0,n=t.length;r<n;r++){if("inline"!==t[r].type||!e.md.linkify.pretest(t[r].content))continue;let n=t[r].children,s=0;for(let o=n.length-1;o>=0;o--){const i=n[o];if("link_close"!==i.type){if("html_inline"===i.type&&(isLinkOpen$1(i.content)&&s>0&&s--,isLinkClose$1(i.content)&&s++),!(s>0)&&"text"===i.type&&e.md.linkify.test(i.content)){const s=i.content;let a=e.md.linkify.match(s);const c=[];let l=i.level,u=0;a.length>0&&0===a[0].index&&o>0&&"text_special"===n[o-1].type&&(a=a.slice(1));for(let t=0;t<a.length;t++){const r=a[t].url,n=e.md.normalizeLink(r);if(!e.md.validateLink(n))continue;let o=a[t].text;o=a[t].schema?"mailto:"!==a[t].schema||/^mailto:/i.test(o)?e.md.normalizeLinkText(o):e.md.normalizeLinkText("mailto:"+o).replace(/^mailto:/,""):e.md.normalizeLinkText("http://"+o).replace(/^http:\/\//,"");const i=a[t].index;if(i>u){const t=new e.Token("text","",0);t.content=s.slice(u,i),t.level=l,c.push(t)}const d=new e.Token("link_open","a",1);d.attrs=[["href",n]],d.level=l++,d.markup="linkify",d.info="auto",c.push(d);const h=new e.Token("text","",0);h.content=o,h.level=l,c.push(h);const p=new e.Token("link_close","a",-1);p.level=--l,p.markup="linkify",p.info="auto",c.push(p),u=a[t].lastIndex}if(u<s.length){const t=new e.Token("text","",0);t.content=s.slice(u),t.level=l,c.push(t)}t[r].children=n=arrayReplaceAt(n,o,c)}}else for(o--;n[o].level!==i.level&&"link_open"!==n[o].type;)o--}}}const RARE_RE=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,SCOPED_ABBR_TEST_RE=/\((c|tm|r)\)/i,SCOPED_ABBR_RE=/\((c|tm|r)\)/gi,SCOPED_ABBR={c:"©",r:"®",tm:"™"};function replaceFn(e,t){return SCOPED_ABBR[t.toLowerCase()]}function replace_scoped(e){let t=0;for(let r=e.length-1;r>=0;r--){const n=e[r];"text"!==n.type||t||(n.content=n.content.replace(SCOPED_ABBR_RE,replaceFn)),"link_open"===n.type&&"auto"===n.info&&t--,"link_close"===n.type&&"auto"===n.info&&t++}}function replace_rare(e){let t=0;for(let r=e.length-1;r>=0;r--){const n=e[r];"text"!==n.type||t||RARE_RE.test(n.content)&&(n.content=n.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/gm,"$1—").replace(/(^|\s)--(?=\s|$)/gm,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm,"$1–")),"link_open"===n.type&&"auto"===n.info&&t--,"link_close"===n.type&&"auto"===n.info&&t++}}function replace(e){let t;if(e.md.options.typographer)for(t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&(SCOPED_ABBR_TEST_RE.test(e.tokens[t].content)&&replace_scoped(e.tokens[t].children),RARE_RE.test(e.tokens[t].content)&&replace_rare(e.tokens[t].children))}const QUOTE_TEST_RE=/['"]/,QUOTE_RE=/['"]/g,APOSTROPHE="’";function replaceAt(e,t,r){return e.slice(0,t)+r+e.slice(t+1)}function process_inlines(e,t){let r;const n=[];for(let s=0;s<e.length;s++){const o=e[s],i=e[s].level;for(r=n.length-1;r>=0&&!(n[r].level<=i);r--);if(n.length=r+1,"text"!==o.type)continue;let a=o.content,c=0,l=a.length;e:for(;c<l;){QUOTE_RE.lastIndex=c;const u=QUOTE_RE.exec(a);if(!u)break;let d=!0,h=!0;c=u.index+1;const p="'"===u[0];let f=32;if(u.index-1>=0)f=a.charCodeAt(u.index-1);else for(r=s-1;r>=0&&("softbreak"!==e[r].type&&"hardbreak"!==e[r].type);r--)if(e[r].content){f=e[r].content.charCodeAt(e[r].content.length-1);break}let m=32;if(c<l)m=a.charCodeAt(c);else for(r=s+1;r<e.length&&("softbreak"!==e[r].type&&"hardbreak"!==e[r].type);r++)if(e[r].content){m=e[r].content.charCodeAt(0);break}const g=isMdAsciiPunct(f)||isPunctChar(String.fromCharCode(f)),_=isMdAsciiPunct(m)||isPunctChar(String.fromCharCode(m)),k=isWhiteSpace(f),C=isWhiteSpace(m);if(C?d=!1:_&&(k||g||(d=!1)),k?h=!1:g&&(C||_||(h=!1)),34===m&&'"'===u[0]&&f>=48&&f<=57&&(h=d=!1),d&&h&&(d=g,h=_),d||h){if(h)for(r=n.length-1;r>=0;r--){let d=n[r];if(n[r].level<i)break;if(d.single===p&&n[r].level===i){let i,h;d=n[r],p?(i=t.md.options.quotes[2],h=t.md.options.quotes[3]):(i=t.md.options.quotes[0],h=t.md.options.quotes[1]),o.content=replaceAt(o.content,u.index,h),e[d.token].content=replaceAt(e[d.token].content,d.pos,i),c+=h.length-1,d.token===s&&(c+=i.length-1),a=o.content,l=a.length,n.length=r;continue e}}d?n.push({token:s,pos:u.index,single:p,level:i}):h&&p&&(o.content=replaceAt(o.content,u.index,"’"))}else p&&(o.content=replaceAt(o.content,u.index,"’"))}}}function smartquotes(e){if(e.md.options.typographer)for(let t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&QUOTE_TEST_RE.test(e.tokens[t].content)&&process_inlines(e.tokens[t].children,e)}function text_join(e){let t,r;const n=e.tokens,s=n.length;for(let e=0;e<s;e++){if("inline"!==n[e].type)continue;const s=n[e].children,o=s.length;for(t=0;t<o;t++)"text_special"===s[t].type&&(s[t].type="text");for(t=r=0;t<o;t++)"text"===s[t].type&&t+1<o&&"text"===s[t+1].type?s[t+1].content=s[t].content+s[t+1].content:(t!==r&&(s[r]=s[t]),r++);t!==r&&(s.length=r)}}const _rules$2=[["normalize",normalize],["block",block],["inline",inline],["linkify",linkify$1],["replacements",replace],["smartquotes",smartquotes],["text_join",text_join]];function Core(){this.ruler=new Ruler;for(let e=0;e<_rules$2.length;e++)this.ruler.push(_rules$2[e][0],_rules$2[e][1])}function StateBlock(e,t,r,n){this.src=e,this.md=t,this.env=r,this.tokens=n,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0;const s=this.src;for(let e=0,t=0,r=0,n=0,o=s.length,i=!1;t<o;t++){const a=s.charCodeAt(t);if(!i){if(isSpace(a)){r++,9===a?n+=4-n%4:n++;continue}i=!0}10!==a&&t!==o-1||(10!==a&&t++,this.bMarks.push(e),this.eMarks.push(t),this.tShift.push(r),this.sCount.push(n),this.bsCount.push(0),i=!1,r=0,n=0,e=t+1)}this.bMarks.push(s.length),this.eMarks.push(s.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}Core.prototype.process=function(e){const t=this.ruler.getRules("");for(let r=0,n=t.length;r<n;r++)t[r](e)},Core.prototype.State=StateCore,StateBlock.prototype.push=function(e,t,r){const n=new Token(e,t,r);return n.block=!0,r<0&&this.level--,n.level=this.level,r>0&&this.level++,this.tokens.push(n),n},StateBlock.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},StateBlock.prototype.skipEmptyLines=function(e){for(let t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},StateBlock.prototype.skipSpaces=function(e){for(let t=this.src.length;e<t;e++){if(!isSpace(this.src.charCodeAt(e)))break}return e},StateBlock.prototype.skipSpacesBack=function(e,t){if(e<=t)return e;for(;e>t;)if(!isSpace(this.src.charCodeAt(--e)))return e+1;return e},StateBlock.prototype.skipChars=function(e,t){for(let r=this.src.length;e<r&&this.src.charCodeAt(e)===t;e++);return e},StateBlock.prototype.skipCharsBack=function(e,t,r){if(e<=r)return e;for(;e>r;)if(t!==this.src.charCodeAt(--e))return e+1;return e},StateBlock.prototype.getLines=function(e,t,r,n){if(e>=t)return"";const s=new Array(t-e);for(let o=0,i=e;i<t;i++,o++){let e=0;const a=this.bMarks[i];let c,l=a;for(c=i+1<t||n?this.eMarks[i]+1:this.eMarks[i];l<c&&e<r;){const t=this.src.charCodeAt(l);if(isSpace(t))9===t?e+=4-(e+this.bsCount[i])%4:e++;else{if(!(l-a<this.tShift[i]))break;e++}l++}s[o]=e>r?new Array(e-r+1).join(" ")+this.src.slice(l,c):this.src.slice(l,c)}return s.join("")},StateBlock.prototype.Token=Token;const MAX_AUTOCOMPLETED_CELLS=65536;function getLine(e,t){const r=e.bMarks[t]+e.tShift[t],n=e.eMarks[t];return e.src.slice(r,n)}function escapedSplit(e){const t=[],r=e.length;let n=0,s=e.charCodeAt(n),o=!1,i=0,a="";for(;n<r;)124===s&&(o?(a+=e.substring(i,n-1),i=n):(t.push(a+e.substring(i,n)),a="",i=n+1)),o=92===s,n++,s=e.charCodeAt(n);return t.push(a+e.substring(i)),t}function table(e,t,r,n){if(t+2>r)return!1;let s=t+1;if(e.sCount[s]<e.blkIndent)return!1;if(e.sCount[s]-e.blkIndent>=4)return!1;let o=e.bMarks[s]+e.tShift[s];if(o>=e.eMarks[s])return!1;const i=e.src.charCodeAt(o++);if(124!==i&&45!==i&&58!==i)return!1;if(o>=e.eMarks[s])return!1;const a=e.src.charCodeAt(o++);if(124!==a&&45!==a&&58!==a&&!isSpace(a))return!1;if(45===i&&isSpace(a))return!1;for(;o<e.eMarks[s];){const t=e.src.charCodeAt(o);if(124!==t&&45!==t&&58!==t&&!isSpace(t))return!1;o++}let c=getLine(e,t+1),l=c.split("|");const u=[];for(let e=0;e<l.length;e++){const t=l[e].trim();if(!t){if(0===e||e===l.length-1)continue;return!1}if(!/^:?-+:?$/.test(t))return!1;58===t.charCodeAt(t.length-1)?u.push(58===t.charCodeAt(0)?"center":"right"):58===t.charCodeAt(0)?u.push("left"):u.push("")}if(c=getLine(e,t).trim(),-1===c.indexOf("|"))return!1;if(e.sCount[t]-e.blkIndent>=4)return!1;l=escapedSplit(c),l.length&&""===l[0]&&l.shift(),l.length&&""===l[l.length-1]&&l.pop();const d=l.length;if(0===d||d!==u.length)return!1;if(n)return!0;const h=e.parentType;e.parentType="table";const p=e.md.block.ruler.getRules("blockquote"),f=[t,0];e.push("table_open","table",1).map=f;e.push("thead_open","thead",1).map=[t,t+1];e.push("tr_open","tr",1).map=[t,t+1];for(let t=0;t<l.length;t++){const r=e.push("th_open","th",1);u[t]&&(r.attrs=[["style","text-align:"+u[t]]]);const n=e.push("inline","",0);n.content=l[t].trim(),n.children=[],e.push("th_close","th",-1)}let m;e.push("tr_close","tr",-1),e.push("thead_close","thead",-1);let g=0;for(s=t+2;s<r&&!(e.sCount[s]<e.blkIndent);s++){let n=!1;for(let t=0,o=p.length;t<o;t++)if(p[t](e,s,r,!0)){n=!0;break}if(n)break;if(c=getLine(e,s).trim(),!c)break;if(e.sCount[s]-e.blkIndent>=4)break;if(l=escapedSplit(c),l.length&&""===l[0]&&l.shift(),l.length&&""===l[l.length-1]&&l.pop(),g+=d-l.length,g>65536)break;if(s===t+2){e.push("tbody_open","tbody",1).map=m=[t+2,0]}e.push("tr_open","tr",1).map=[s,s+1];for(let t=0;t<d;t++){const r=e.push("td_open","td",1);u[t]&&(r.attrs=[["style","text-align:"+u[t]]]);const n=e.push("inline","",0);n.content=l[t]?l[t].trim():"",n.children=[],e.push("td_close","td",-1)}e.push("tr_close","tr",-1)}return m&&(e.push("tbody_close","tbody",-1),m[1]=s),e.push("table_close","table",-1),f[1]=s,e.parentType=h,e.line=s,!0}function code(e,t,r){if(e.sCount[t]-e.blkIndent<4)return!1;let n=t+1,s=n;for(;n<r;)if(e.isEmpty(n))n++;else{if(!(e.sCount[n]-e.blkIndent>=4))break;n++,s=n}e.line=s;const o=e.push("code_block","code",0);return o.content=e.getLines(t,s,4+e.blkIndent,!1)+"\n",o.map=[t,e.line],!0}function fence(e,t,r,n){let s=e.bMarks[t]+e.tShift[t],o=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(s+3>o)return!1;const i=e.src.charCodeAt(s);if(126!==i&&96!==i)return!1;let a=s;s=e.skipChars(s,i);let c=s-a;if(c<3)return!1;const l=e.src.slice(a,s),u=e.src.slice(s,o);if(96===i&&u.indexOf(String.fromCharCode(i))>=0)return!1;if(n)return!0;let d=t,h=!1;for(;(d++,!(d>=r))&&(s=a=e.bMarks[d]+e.tShift[d],o=e.eMarks[d],!(s<o&&e.sCount[d]<e.blkIndent));)if(e.src.charCodeAt(s)===i&&!(e.sCount[d]-e.blkIndent>=4||(s=e.skipChars(s,i),s-a<c||(s=e.skipSpaces(s),s<o)))){h=!0;break}c=e.sCount[t],e.line=d+(h?1:0);const p=e.push("fence","code",0);return p.info=u,p.content=e.getLines(t+1,d,c,!0),p.markup=l,p.map=[t,e.line],!0}function blockquote(e,t,r,n){let s=e.bMarks[t]+e.tShift[t],o=e.eMarks[t];const i=e.lineMax;if(e.sCount[t]-e.blkIndent>=4)return!1;if(62!==e.src.charCodeAt(s))return!1;if(n)return!0;const a=[],c=[],l=[],u=[],d=e.md.block.ruler.getRules("blockquote"),h=e.parentType;e.parentType="blockquote";let p,f=!1;for(p=t;p<r;p++){const t=e.sCount[p]<e.blkIndent;if(s=e.bMarks[p]+e.tShift[p],o=e.eMarks[p],s>=o)break;if(62===e.src.charCodeAt(s++)&&!t){let t,r,n=e.sCount[p]+1;32===e.src.charCodeAt(s)?(s++,n++,r=!1,t=!0):9===e.src.charCodeAt(s)?(t=!0,(e.bsCount[p]+n)%4==3?(s++,n++,r=!1):r=!0):t=!1;let i=n;for(a.push(e.bMarks[p]),e.bMarks[p]=s;s<o;){const t=e.src.charCodeAt(s);if(!isSpace(t))break;9===t?i+=4-(i+e.bsCount[p]+(r?1:0))%4:i++,s++}f=s>=o,c.push(e.bsCount[p]),e.bsCount[p]=e.sCount[p]+1+(t?1:0),l.push(e.sCount[p]),e.sCount[p]=i-n,u.push(e.tShift[p]),e.tShift[p]=s-e.bMarks[p];continue}if(f)break;let n=!1;for(let t=0,s=d.length;t<s;t++)if(d[t](e,p,r,!0)){n=!0;break}if(n){e.lineMax=p,0!==e.blkIndent&&(a.push(e.bMarks[p]),c.push(e.bsCount[p]),u.push(e.tShift[p]),l.push(e.sCount[p]),e.sCount[p]-=e.blkIndent);break}a.push(e.bMarks[p]),c.push(e.bsCount[p]),u.push(e.tShift[p]),l.push(e.sCount[p]),e.sCount[p]=-1}const m=e.blkIndent;e.blkIndent=0;const g=e.push("blockquote_open","blockquote",1);g.markup=">";const _=[t,0];g.map=_,e.md.block.tokenize(e,t,p);e.push("blockquote_close","blockquote",-1).markup=">",e.lineMax=i,e.parentType=h,_[1]=e.line;for(let r=0;r<u.length;r++)e.bMarks[r+t]=a[r],e.tShift[r+t]=u[r],e.sCount[r+t]=l[r],e.bsCount[r+t]=c[r];return e.blkIndent=m,!0}function hr(e,t,r,n){const s=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;let o=e.bMarks[t]+e.tShift[t];const i=e.src.charCodeAt(o++);if(42!==i&&45!==i&&95!==i)return!1;let a=1;for(;o<s;){const t=e.src.charCodeAt(o++);if(t!==i&&!isSpace(t))return!1;t===i&&a++}if(a<3)return!1;if(n)return!0;e.line=t+1;const c=e.push("hr","hr",0);return c.map=[t,e.line],c.markup=Array(a+1).join(String.fromCharCode(i)),!0}function skipBulletListMarker(e,t){const r=e.eMarks[t];let n=e.bMarks[t]+e.tShift[t];const s=e.src.charCodeAt(n++);if(42!==s&&45!==s&&43!==s)return-1;if(n<r){if(!isSpace(e.src.charCodeAt(n)))return-1}return n}function skipOrderedListMarker(e,t){const r=e.bMarks[t]+e.tShift[t],n=e.eMarks[t];let s=r;if(s+1>=n)return-1;let o=e.src.charCodeAt(s++);if(o<48||o>57)return-1;for(;;){if(s>=n)return-1;if(o=e.src.charCodeAt(s++),!(o>=48&&o<=57)){if(41===o||46===o)break;return-1}if(s-r>=10)return-1}return s<n&&(o=e.src.charCodeAt(s),!isSpace(o))?-1:s}function markTightParagraphs(e,t){const r=e.level+2;for(let n=t+2,s=e.tokens.length-2;n<s;n++)e.tokens[n].level===r&&"paragraph_open"===e.tokens[n].type&&(e.tokens[n+2].hidden=!0,e.tokens[n].hidden=!0,n+=2)}function list(e,t,r,n){let s,o,i,a,c=t,l=!0;if(e.sCount[c]-e.blkIndent>=4)return!1;if(e.listIndent>=0&&e.sCount[c]-e.listIndent>=4&&e.sCount[c]<e.blkIndent)return!1;let u,d,h,p=!1;if(n&&"paragraph"===e.parentType&&e.sCount[c]>=e.blkIndent&&(p=!0),(h=skipOrderedListMarker(e,c))>=0){if(u=!0,i=e.bMarks[c]+e.tShift[c],d=Number(e.src.slice(i,h-1)),p&&1!==d)return!1}else{if(!((h=skipBulletListMarker(e,c))>=0))return!1;u=!1}if(p&&e.skipSpaces(h)>=e.eMarks[c])return!1;if(n)return!0;const f=e.src.charCodeAt(h-1),m=e.tokens.length;u?(a=e.push("ordered_list_open","ol",1),1!==d&&(a.attrs=[["start",d]])):a=e.push("bullet_list_open","ul",1);const g=[c,0];a.map=g,a.markup=String.fromCharCode(f);let _=!1;const k=e.md.block.ruler.getRules("list"),C=e.parentType;for(e.parentType="list";c<r;){o=h,s=e.eMarks[c];const t=e.sCount[c]+h-(e.bMarks[c]+e.tShift[c]);let n=t;for(;o<s;){const t=e.src.charCodeAt(o);if(9===t)n+=4-(n+e.bsCount[c])%4;else{if(32!==t)break;n++}o++}const d=o;let p;p=d>=s?1:n-t,p>4&&(p=1);const m=t+p;a=e.push("list_item_open","li",1),a.markup=String.fromCharCode(f);const g=[c,0];a.map=g,u&&(a.info=e.src.slice(i,h-1));const C=e.tight,y=e.tShift[c],b=e.sCount[c],E=e.listIndent;if(e.listIndent=e.blkIndent,e.blkIndent=m,e.tight=!0,e.tShift[c]=d-e.bMarks[c],e.sCount[c]=n,d>=s&&e.isEmpty(c+1)?e.line=Math.min(e.line+2,r):e.md.block.tokenize(e,c,r,!0),e.tight&&!_||(l=!1),_=e.line-c>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=E,e.tShift[c]=y,e.sCount[c]=b,e.tight=C,a=e.push("list_item_close","li",-1),a.markup=String.fromCharCode(f),c=e.line,g[1]=c,c>=r)break;if(e.sCount[c]<e.blkIndent)break;if(e.sCount[c]-e.blkIndent>=4)break;let D=!1;for(let t=0,n=k.length;t<n;t++)if(k[t](e,c,r,!0)){D=!0;break}if(D)break;if(u){if(h=skipOrderedListMarker(e,c),h<0)break;i=e.bMarks[c]+e.tShift[c]}else if(h=skipBulletListMarker(e,c),h<0)break;if(f!==e.src.charCodeAt(h-1))break}return a=u?e.push("ordered_list_close","ol",-1):e.push("bullet_list_close","ul",-1),a.markup=String.fromCharCode(f),g[1]=c,e.line=c,e.parentType=C,l&&markTightParagraphs(e,m),!0}function reference(e,t,r,n){let s=e.bMarks[t]+e.tShift[t],o=e.eMarks[t],i=t+1;if(e.sCount[t]-e.blkIndent>=4)return!1;if(91!==e.src.charCodeAt(s))return!1;function a(t){const r=e.lineMax;if(t>=r||e.isEmpty(t))return null;let n=!1;if(e.sCount[t]-e.blkIndent>3&&(n=!0),e.sCount[t]<0&&(n=!0),!n){const n=e.md.block.ruler.getRules("reference"),s=e.parentType;e.parentType="reference";let o=!1;for(let s=0,i=n.length;s<i;s++)if(n[s](e,t,r,!0)){o=!0;break}if(e.parentType=s,o)return null}const s=e.bMarks[t]+e.tShift[t],o=e.eMarks[t];return e.src.slice(s,o+1)}let c=e.src.slice(s,o+1);o=c.length;let l=-1;for(s=1;s<o;s++){const e=c.charCodeAt(s);if(91===e)return!1;if(93===e){l=s;break}if(10===e){const e=a(i);null!==e&&(c+=e,o=c.length,i++)}else if(92===e&&(s++,s<o&&10===c.charCodeAt(s))){const e=a(i);null!==e&&(c+=e,o=c.length,i++)}}if(l<0||58!==c.charCodeAt(l+1))return!1;for(s=l+2;s<o;s++){const e=c.charCodeAt(s);if(10===e){const e=a(i);null!==e&&(c+=e,o=c.length,i++)}else if(!isSpace(e))break}const u=e.md.helpers.parseLinkDestination(c,s,o);if(!u.ok)return!1;const d=e.md.normalizeLink(u.str);if(!e.md.validateLink(d))return!1;s=u.pos;const h=s,p=i,f=s;for(;s<o;s++){const e=c.charCodeAt(s);if(10===e){const e=a(i);null!==e&&(c+=e,o=c.length,i++)}else if(!isSpace(e))break}let m,g=e.md.helpers.parseLinkTitle(c,s,o);for(;g.can_continue;){const t=a(i);if(null===t)break;c+=t,s=o,o=c.length,i++,g=e.md.helpers.parseLinkTitle(c,s,o,g)}for(s<o&&f!==s&&g.ok?(m=g.str,s=g.pos):(m="",s=h,i=p);s<o;){if(!isSpace(c.charCodeAt(s)))break;s++}if(s<o&&10!==c.charCodeAt(s)&&m)for(m="",s=h,i=p;s<o;){if(!isSpace(c.charCodeAt(s)))break;s++}if(s<o&&10!==c.charCodeAt(s))return!1;const _=normalizeReference(c.slice(1,l));return!!_&&(n||(void 0===e.env.references&&(e.env.references={}),void 0===e.env.references[_]&&(e.env.references[_]={title:m,href:d}),e.line=i),!0)}const block_names=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],attr_name="[a-zA-Z_:][a-zA-Z0-9:._-]*",unquoted="[^\"'=<>`\\x00-\\x20]+",single_quoted="'[^']*'",double_quoted='"[^"]*"',attr_value="(?:"+unquoted+"|'[^']*'|\"[^\"]*\")",attribute="(?:\\s+"+attr_name+"(?:\\s*=\\s*"+attr_value+")?)",open_tag="<[A-Za-z][A-Za-z0-9\\-]*"+attribute+"*\\s*\\/?>",close_tag="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",comment="\x3c!---?>|\x3c!--(?:[^-]|-[^-]|--[^>])*--\x3e",processing="<[?][\\s\\S]*?[?]>",declaration="<![A-Za-z][^>]*>",cdata="<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",HTML_TAG_RE=new RegExp("^(?:"+open_tag+"|"+close_tag+"|"+comment+"|"+processing+"|"+declaration+"|"+cdata+")"),HTML_OPEN_CLOSE_TAG_RE=new RegExp("^(?:"+open_tag+"|"+close_tag+")"),HTML_SEQUENCES=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+block_names.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(HTML_OPEN_CLOSE_TAG_RE.source+"\\s*$"),/^$/,!1]];function html_block(e,t,r,n){let s=e.bMarks[t]+e.tShift[t],o=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(!e.md.options.html)return!1;if(60!==e.src.charCodeAt(s))return!1;let i=e.src.slice(s,o),a=0;for(;a<HTML_SEQUENCES.length&&!HTML_SEQUENCES[a][0].test(i);a++);if(a===HTML_SEQUENCES.length)return!1;if(n)return HTML_SEQUENCES[a][2];let c=t+1;if(!HTML_SEQUENCES[a][1].test(i))for(;c<r&&!(e.sCount[c]<e.blkIndent);c++)if(s=e.bMarks[c]+e.tShift[c],o=e.eMarks[c],i=e.src.slice(s,o),HTML_SEQUENCES[a][1].test(i)){0!==i.length&&c++;break}e.line=c;const l=e.push("html_block","",0);return l.map=[t,c],l.content=e.getLines(t,c,e.blkIndent,!0),!0}function heading(e,t,r,n){let s=e.bMarks[t]+e.tShift[t],o=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;let i=e.src.charCodeAt(s);if(35!==i||s>=o)return!1;let a=1;for(i=e.src.charCodeAt(++s);35===i&&s<o&&a<=6;)a++,i=e.src.charCodeAt(++s);if(a>6||s<o&&!isSpace(i))return!1;if(n)return!0;o=e.skipSpacesBack(o,s);const c=e.skipCharsBack(o,35,s);c>s&&isSpace(e.src.charCodeAt(c-1))&&(o=c),e.line=t+1;const l=e.push("heading_open","h"+String(a),1);l.markup="########".slice(0,a),l.map=[t,e.line];const u=e.push("inline","",0);u.content=e.src.slice(s,o).trim(),u.map=[t,e.line],u.children=[];return e.push("heading_close","h"+String(a),-1).markup="########".slice(0,a),!0}function lheading(e,t,r){const n=e.md.block.ruler.getRules("paragraph");if(e.sCount[t]-e.blkIndent>=4)return!1;const s=e.parentType;e.parentType="paragraph";let o,i=0,a=t+1;for(;a<r&&!e.isEmpty(a);a++){if(e.sCount[a]-e.blkIndent>3)continue;if(e.sCount[a]>=e.blkIndent){let t=e.bMarks[a]+e.tShift[a];const r=e.eMarks[a];if(t<r&&(o=e.src.charCodeAt(t),(45===o||61===o)&&(t=e.skipChars(t,o),t=e.skipSpaces(t),t>=r))){i=61===o?1:2;break}}if(e.sCount[a]<0)continue;let t=!1;for(let s=0,o=n.length;s<o;s++)if(n[s](e,a,r,!0)){t=!0;break}if(t)break}if(!i)return!1;const c=e.getLines(t,a,e.blkIndent,!1).trim();e.line=a+1;const l=e.push("heading_open","h"+String(i),1);l.markup=String.fromCharCode(o),l.map=[t,e.line];const u=e.push("inline","",0);u.content=c,u.map=[t,e.line-1],u.children=[];return e.push("heading_close","h"+String(i),-1).markup=String.fromCharCode(o),e.parentType=s,!0}function paragraph(e,t,r){const n=e.md.block.ruler.getRules("paragraph"),s=e.parentType;let o=t+1;for(e.parentType="paragraph";o<r&&!e.isEmpty(o);o++){if(e.sCount[o]-e.blkIndent>3)continue;if(e.sCount[o]<0)continue;let t=!1;for(let s=0,i=n.length;s<i;s++)if(n[s](e,o,r,!0)){t=!0;break}if(t)break}const i=e.getLines(t,o,e.blkIndent,!1).trim();e.line=o;e.push("paragraph_open","p",1).map=[t,e.line];const a=e.push("inline","",0);return a.content=i,a.map=[t,e.line],a.children=[],e.push("paragraph_close","p",-1),e.parentType=s,!0}const _rules$1=[["table",table,["paragraph","reference"]],["code",code],["fence",fence,["paragraph","reference","blockquote","list"]],["blockquote",blockquote,["paragraph","reference","blockquote","list"]],["hr",hr,["paragraph","reference","blockquote","list"]],["list",list,["paragraph","reference","blockquote"]],["reference",reference],["html_block",html_block,["paragraph","reference","blockquote"]],["heading",heading,["paragraph","reference","blockquote"]],["lheading",lheading],["paragraph",paragraph]];function ParserBlock(){this.ruler=new Ruler;for(let e=0;e<_rules$1.length;e++)this.ruler.push(_rules$1[e][0],_rules$1[e][1],{alt:(_rules$1[e][2]||[]).slice()})}function StateInline(e,t,r,n){this.src=e,this.env=r,this.md=t,this.tokens=n,this.tokens_meta=Array(n.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}function isTerminatorChar(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}function text(e,t){let r=e.pos;for(;r<e.posMax&&!isTerminatorChar(e.src.charCodeAt(r));)r++;return r!==e.pos&&(t||(e.pending+=e.src.slice(e.pos,r)),e.pos=r,!0)}ParserBlock.prototype.tokenize=function(e,t,r){const n=this.ruler.getRules(""),s=n.length,o=e.md.options.maxNesting;let i=t,a=!1;for(;i<r&&(e.line=i=e.skipEmptyLines(i),!(i>=r))&&!(e.sCount[i]<e.blkIndent);){if(e.level>=o){e.line=r;break}const t=e.line;let c=!1;for(let o=0;o<s;o++)if(c=n[o](e,i,r,!1),c){if(t>=e.line)throw new Error("block rule didn't increment state.line");break}if(!c)throw new Error("none of the block rules matched");e.tight=!a,e.isEmpty(e.line-1)&&(a=!0),i=e.line,i<r&&e.isEmpty(i)&&(a=!0,i++,e.line=i)}},ParserBlock.prototype.parse=function(e,t,r,n){if(!e)return;const s=new this.State(e,t,r,n);this.tokenize(s,s.line,s.lineMax)},ParserBlock.prototype.State=StateBlock,StateInline.prototype.pushPending=function(){const e=new Token("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e},StateInline.prototype.push=function(e,t,r){this.pending&&this.pushPending();const n=new Token(e,t,r);let s=null;return r<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),n.level=this.level,r>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],s={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(n),this.tokens_meta.push(s),n},StateInline.prototype.scanDelims=function(e,t){const r=this.posMax,n=this.src.charCodeAt(e),s=e>0?this.src.charCodeAt(e-1):32;let o=e;for(;o<r&&this.src.charCodeAt(o)===n;)o++;const i=o-e,a=o<r?this.src.charCodeAt(o):32,c=isMdAsciiPunct(s)||isPunctChar(String.fromCharCode(s)),l=isMdAsciiPunct(a)||isPunctChar(String.fromCharCode(a)),u=isWhiteSpace(s),d=isWhiteSpace(a),h=!d&&(!l||u||c),p=!u&&(!c||d||l);return{can_open:h&&(t||!p||c),can_close:p&&(t||!h||l),length:i}},StateInline.prototype.Token=Token;const SCHEME_RE=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;function linkify(e,t){if(!e.md.options.linkify)return!1;if(e.linkLevel>0)return!1;const r=e.pos;if(r+3>e.posMax)return!1;if(58!==e.src.charCodeAt(r))return!1;if(47!==e.src.charCodeAt(r+1))return!1;if(47!==e.src.charCodeAt(r+2))return!1;const n=e.pending.match(SCHEME_RE);if(!n)return!1;const s=n[1],o=e.md.linkify.matchAtStart(e.src.slice(r-s.length));if(!o)return!1;let i=o.url;if(i.length<=s.length)return!1;i=i.replace(/\*+$/,"");const a=e.md.normalizeLink(i);if(!e.md.validateLink(a))return!1;if(!t){e.pending=e.pending.slice(0,-s.length);const t=e.push("link_open","a",1);t.attrs=[["href",a]],t.markup="linkify",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(i);const r=e.push("link_close","a",-1);r.markup="linkify",r.info="auto"}return e.pos+=i.length-s.length,!0}function newline(e,t){let r=e.pos;if(10!==e.src.charCodeAt(r))return!1;const n=e.pending.length-1,s=e.posMax;if(!t)if(n>=0&&32===e.pending.charCodeAt(n))if(n>=1&&32===e.pending.charCodeAt(n-1)){let t=n-1;for(;t>=1&&32===e.pending.charCodeAt(t-1);)t--;e.pending=e.pending.slice(0,t),e.push("hardbreak","br",0)}else e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0);else e.push("softbreak","br",0);for(r++;r<s&&isSpace(e.src.charCodeAt(r));)r++;return e.pos=r,!0}const ESCAPED=[];for(let e=0;e<256;e++)ESCAPED.push(0);function escape(e,t){let r=e.pos;const n=e.posMax;if(92!==e.src.charCodeAt(r))return!1;if(r++,r>=n)return!1;let s=e.src.charCodeAt(r);if(10===s){for(t||e.push("hardbreak","br",0),r++;r<n&&(s=e.src.charCodeAt(r),isSpace(s));)r++;return e.pos=r,!0}let o=e.src[r];if(s>=55296&&s<=56319&&r+1<n){const t=e.src.charCodeAt(r+1);t>=56320&&t<=57343&&(o+=e.src[r+1],r++)}const i="\\"+o;if(!t){const t=e.push("text_special","",0);s<256&&0!==ESCAPED[s]?t.content=o:t.content=i,t.markup=i,t.info="escape"}return e.pos=r+1,!0}function backtick(e,t){let r=e.pos;if(96!==e.src.charCodeAt(r))return!1;const n=r;r++;const s=e.posMax;for(;r<s&&96===e.src.charCodeAt(r);)r++;const o=e.src.slice(n,r),i=o.length;if(e.backticksScanned&&(e.backticks[i]||0)<=n)return t||(e.pending+=o),e.pos+=i,!0;let a,c=r;for(;-1!==(a=e.src.indexOf("`",c));){for(c=a+1;c<s&&96===e.src.charCodeAt(c);)c++;const n=c-a;if(n===i){if(!t){const t=e.push("code_inline","code",0);t.markup=o,t.content=e.src.slice(r,a).replace(/\n/g," ").replace(/^ (.+) $/,"$1")}return e.pos=c,!0}e.backticks[n]=a}return e.backticksScanned=!0,t||(e.pending+=o),e.pos+=i,!0}function strikethrough_tokenize(e,t){const r=e.pos,n=e.src.charCodeAt(r);if(t)return!1;if(126!==n)return!1;const s=e.scanDelims(e.pos,!0);let o=s.length;const i=String.fromCharCode(n);if(o<2)return!1;let a;o%2&&(a=e.push("text","",0),a.content=i,o--);for(let t=0;t<o;t+=2)a=e.push("text","",0),a.content=i+i,e.delimiters.push({marker:n,length:0,token:e.tokens.length-1,end:-1,open:s.can_open,close:s.can_close});return e.pos+=s.length,!0}function postProcess$1(e,t){let r;const n=[],s=t.length;for(let o=0;o<s;o++){const s=t[o];if(126!==s.marker)continue;if(-1===s.end)continue;const i=t[s.end];r=e.tokens[s.token],r.type="s_open",r.tag="s",r.nesting=1,r.markup="~~",r.content="",r=e.tokens[i.token],r.type="s_close",r.tag="s",r.nesting=-1,r.markup="~~",r.content="","text"===e.tokens[i.token-1].type&&"~"===e.tokens[i.token-1].content&&n.push(i.token-1)}for(;n.length;){const t=n.pop();let s=t+1;for(;s<e.tokens.length&&"s_close"===e.tokens[s].type;)s++;s--,t!==s&&(r=e.tokens[s],e.tokens[s]=e.tokens[t],e.tokens[t]=r)}}function strikethrough_postProcess(e){const t=e.tokens_meta,r=e.tokens_meta.length;postProcess$1(e,e.delimiters);for(let n=0;n<r;n++)t[n]&&t[n].delimiters&&postProcess$1(e,t[n].delimiters)}"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(e){ESCAPED[e.charCodeAt(0)]=1}));const r_strikethrough={tokenize:strikethrough_tokenize,postProcess:strikethrough_postProcess};function emphasis_tokenize(e,t){const r=e.pos,n=e.src.charCodeAt(r);if(t)return!1;if(95!==n&&42!==n)return!1;const s=e.scanDelims(e.pos,42===n);for(let t=0;t<s.length;t++){e.push("text","",0).content=String.fromCharCode(n),e.delimiters.push({marker:n,length:s.length,token:e.tokens.length-1,end:-1,open:s.can_open,close:s.can_close})}return e.pos+=s.length,!0}function postProcess(e,t){for(let r=t.length-1;r>=0;r--){const n=t[r];if(95!==n.marker&&42!==n.marker)continue;if(-1===n.end)continue;const s=t[n.end],o=r>0&&t[r-1].end===n.end+1&&t[r-1].marker===n.marker&&t[r-1].token===n.token-1&&t[n.end+1].token===s.token+1,i=String.fromCharCode(n.marker),a=e.tokens[n.token];a.type=o?"strong_open":"em_open",a.tag=o?"strong":"em",a.nesting=1,a.markup=o?i+i:i,a.content="";const c=e.tokens[s.token];c.type=o?"strong_close":"em_close",c.tag=o?"strong":"em",c.nesting=-1,c.markup=o?i+i:i,c.content="",o&&(e.tokens[t[r-1].token].content="",e.tokens[t[n.end+1].token].content="",r--)}}function emphasis_post_process(e){const t=e.tokens_meta,r=e.tokens_meta.length;postProcess(e,e.delimiters);for(let n=0;n<r;n++)t[n]&&t[n].delimiters&&postProcess(e,t[n].delimiters)}const r_emphasis={tokenize:emphasis_tokenize,postProcess:emphasis_post_process};function link(e,t){let r,n,s,o,i="",a="",c=e.pos,l=!0;if(91!==e.src.charCodeAt(e.pos))return!1;const u=e.pos,d=e.posMax,h=e.pos+1,p=e.md.helpers.parseLinkLabel(e,e.pos,!0);if(p<0)return!1;let f=p+1;if(f<d&&40===e.src.charCodeAt(f)){for(l=!1,f++;f<d&&(r=e.src.charCodeAt(f),isSpace(r)||10===r);f++);if(f>=d)return!1;if(c=f,s=e.md.helpers.parseLinkDestination(e.src,f,e.posMax),s.ok){for(i=e.md.normalizeLink(s.str),e.md.validateLink(i)?f=s.pos:i="",c=f;f<d&&(r=e.src.charCodeAt(f),isSpace(r)||10===r);f++);if(s=e.md.helpers.parseLinkTitle(e.src,f,e.posMax),f<d&&c!==f&&s.ok)for(a=s.str,f=s.pos;f<d&&(r=e.src.charCodeAt(f),isSpace(r)||10===r);f++);}(f>=d||41!==e.src.charCodeAt(f))&&(l=!0),f++}if(l){if(void 0===e.env.references)return!1;if(f<d&&91===e.src.charCodeAt(f)?(c=f+1,f=e.md.helpers.parseLinkLabel(e,f),f>=0?n=e.src.slice(c,f++):f=p+1):f=p+1,n||(n=e.src.slice(h,p)),o=e.env.references[normalizeReference(n)],!o)return e.pos=u,!1;i=o.href,a=o.title}if(!t){e.pos=h,e.posMax=p;const t=[["href",i]];e.push("link_open","a",1).attrs=t,a&&t.push(["title",a]),e.linkLevel++,e.md.inline.tokenize(e),e.linkLevel--,e.push("link_close","a",-1)}return e.pos=f,e.posMax=d,!0}function image(e,t){let r,n,s,o,i,a,c,l,u="";const d=e.pos,h=e.posMax;if(33!==e.src.charCodeAt(e.pos))return!1;if(91!==e.src.charCodeAt(e.pos+1))return!1;const p=e.pos+2,f=e.md.helpers.parseLinkLabel(e,e.pos+1,!1);if(f<0)return!1;if(o=f+1,o<h&&40===e.src.charCodeAt(o)){for(o++;o<h&&(r=e.src.charCodeAt(o),isSpace(r)||10===r);o++);if(o>=h)return!1;for(l=o,a=e.md.helpers.parseLinkDestination(e.src,o,e.posMax),a.ok&&(u=e.md.normalizeLink(a.str),e.md.validateLink(u)?o=a.pos:u=""),l=o;o<h&&(r=e.src.charCodeAt(o),isSpace(r)||10===r);o++);if(a=e.md.helpers.parseLinkTitle(e.src,o,e.posMax),o<h&&l!==o&&a.ok)for(c=a.str,o=a.pos;o<h&&(r=e.src.charCodeAt(o),isSpace(r)||10===r);o++);else c="";if(o>=h||41!==e.src.charCodeAt(o))return e.pos=d,!1;o++}else{if(void 0===e.env.references)return!1;if(o<h&&91===e.src.charCodeAt(o)?(l=o+1,o=e.md.helpers.parseLinkLabel(e,o),o>=0?s=e.src.slice(l,o++):o=f+1):o=f+1,s||(s=e.src.slice(p,f)),i=e.env.references[normalizeReference(s)],!i)return e.pos=d,!1;u=i.href,c=i.title}if(!t){n=e.src.slice(p,f);const t=[];e.md.inline.parse(n,e.md,e.env,t);const r=e.push("image","img",0),s=[["src",u],["alt",""]];r.attrs=s,r.children=t,r.content=n,c&&s.push(["title",c])}return e.pos=o,e.posMax=h,!0}const EMAIL_RE=/^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,AUTOLINK_RE=/^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\x00-\x20]*)$/;function autolink(e,t){let r=e.pos;if(60!==e.src.charCodeAt(r))return!1;const n=e.pos,s=e.posMax;for(;;){if(++r>=s)return!1;const t=e.src.charCodeAt(r);if(60===t)return!1;if(62===t)break}const o=e.src.slice(n+1,r);if(AUTOLINK_RE.test(o)){const r=e.md.normalizeLink(o);if(!e.md.validateLink(r))return!1;if(!t){const t=e.push("link_open","a",1);t.attrs=[["href",r]],t.markup="autolink",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(o);const n=e.push("link_close","a",-1);n.markup="autolink",n.info="auto"}return e.pos+=o.length+2,!0}if(EMAIL_RE.test(o)){const r=e.md.normalizeLink("mailto:"+o);if(!e.md.validateLink(r))return!1;if(!t){const t=e.push("link_open","a",1);t.attrs=[["href",r]],t.markup="autolink",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(o);const n=e.push("link_close","a",-1);n.markup="autolink",n.info="auto"}return e.pos+=o.length+2,!0}return!1}function isLinkOpen(e){return/^<a[>\s]/i.test(e)}function isLinkClose(e){return/^<\/a\s*>/i.test(e)}function isLetter(e){const t=32|e;return t>=97&&t<=122}function html_inline(e,t){if(!e.md.options.html)return!1;const r=e.posMax,n=e.pos;if(60!==e.src.charCodeAt(n)||n+2>=r)return!1;const s=e.src.charCodeAt(n+1);if(33!==s&&63!==s&&47!==s&&!isLetter(s))return!1;const o=e.src.slice(n).match(HTML_TAG_RE);if(!o)return!1;if(!t){const t=e.push("html_inline","",0);t.content=o[0],isLinkOpen(t.content)&&e.linkLevel++,isLinkClose(t.content)&&e.linkLevel--}return e.pos+=o[0].length,!0}const DIGITAL_RE=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,NAMED_RE=/^&([a-z][a-z0-9]{1,31});/i;function entity(e,t){const r=e.pos,n=e.posMax;if(38!==e.src.charCodeAt(r))return!1;if(r+1>=n)return!1;if(35===e.src.charCodeAt(r+1)){const n=e.src.slice(r).match(DIGITAL_RE);if(n){if(!t){const t="x"===n[1][0].toLowerCase()?parseInt(n[1].slice(1),16):parseInt(n[1],10),r=e.push("text_special","",0);r.content=isValidEntityCode(t)?fromCodePoint(t):fromCodePoint(65533),r.markup=n[0],r.info="entity"}return e.pos+=n[0].length,!0}}else{const n=e.src.slice(r).match(NAMED_RE);if(n){const r=decodeHTML(n[0]);if(r!==n[0]){if(!t){const t=e.push("text_special","",0);t.content=r,t.markup=n[0],t.info="entity"}return e.pos+=n[0].length,!0}}}return!1}function processDelimiters(e){const t={},r=e.length;if(!r)return;let n=0,s=-2;const o=[];for(let i=0;i<r;i++){const r=e[i];if(o.push(0),e[n].marker===r.marker&&s===r.token-1||(n=i),s=r.token,r.length=r.length||0,!r.close)continue;t.hasOwnProperty(r.marker)||(t[r.marker]=[-1,-1,-1,-1,-1,-1]);const a=t[r.marker][(r.open?3:0)+r.length%3];let c=n-o[n]-1,l=c;for(;c>a;c-=o[c]+1){const t=e[c];if(t.marker===r.marker&&(t.open&&t.end<0)){let n=!1;if((t.close||r.open)&&(t.length+r.length)%3==0&&(t.length%3==0&&r.length%3==0||(n=!0)),!n){const n=c>0&&!e[c-1].open?o[c-1]+1:0;o[i]=i-c+n,o[c]=n,r.open=!1,t.end=i,t.close=!1,l=-1,s=-2;break}}}-1!==l&&(t[r.marker][(r.open?3:0)+(r.length||0)%3]=l)}}function link_pairs(e){const t=e.tokens_meta,r=e.tokens_meta.length;processDelimiters(e.delimiters);for(let e=0;e<r;e++)t[e]&&t[e].delimiters&&processDelimiters(t[e].delimiters)}function fragments_join(e){let t,r,n=0;const s=e.tokens,o=e.tokens.length;for(t=r=0;t<o;t++)s[t].nesting<0&&n--,s[t].level=n,s[t].nesting>0&&n++,"text"===s[t].type&&t+1<o&&"text"===s[t+1].type?s[t+1].content=s[t].content+s[t+1].content:(t!==r&&(s[r]=s[t]),r++);t!==r&&(s.length=r)}const _rules=[["text",text],["linkify",linkify],["newline",newline],["escape",escape],["backticks",backtick],["strikethrough",r_strikethrough.tokenize],["emphasis",r_emphasis.tokenize],["link",link],["image",image],["autolink",autolink],["html_inline",html_inline],["entity",entity]],_rules2=[["balance_pairs",link_pairs],["strikethrough",r_strikethrough.postProcess],["emphasis",r_emphasis.postProcess],["fragments_join",fragments_join]];function ParserInline(){this.ruler=new Ruler;for(let e=0;e<_rules.length;e++)this.ruler.push(_rules[e][0],_rules[e][1]);this.ruler2=new Ruler;for(let e=0;e<_rules2.length;e++)this.ruler2.push(_rules2[e][0],_rules2[e][1])}function reFactory(e){const t={};e=e||{},t.src_Any=Any.source,t.src_Cc=Cc.source,t.src_Z=Z.source,t.src_P=P.source,t.src_ZPCc=[t.src_Z,t.src_P,t.src_Cc].join("|"),t.src_ZCc=[t.src_Z,t.src_Cc].join("|");const r="[><｜]";return t.src_pseudo_letter="(?:(?![><｜]|"+t.src_ZPCc+")"+t.src_Any+")",t.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",t.src_auth="(?:(?:(?!"+t.src_ZCc+"|[@/\\[\\]()]).)+@)?",t.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",t.src_host_terminator="(?=$|[><｜]|"+t.src_ZPCc+")(?!"+(e["---"]?"-(?!--)|":"-|")+"_|:\\d|\\.-|\\.(?!$|"+t.src_ZPCc+"))",t.src_path="(?:[/?#](?:(?!"+t.src_ZCc+"|"+r+"|[()[\\]{}.,\"'?!\\-;]).|\\[(?:(?!"+t.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+t.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+t.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+t.src_ZCc+'|["]).)+\\"|\\\'(?:(?!'+t.src_ZCc+"|[']).)+\\'|\\'(?="+t.src_pseudo_letter+"|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+t.src_ZCc+"|[.]|$)|"+(e["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+t.src_ZCc+"|$)|;(?!"+t.src_ZCc+"|$)|\\!+(?!"+t.src_ZCc+"|[!]|$)|\\?(?!"+t.src_ZCc+"|[?]|$))+|\\/)?",t.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',t.src_xn="xn--[a-z0-9\\-]{1,59}",t.src_domain_root="(?:"+t.src_xn+"|"+t.src_pseudo_letter+"{1,63})",t.src_domain="(?:"+t.src_xn+"|(?:"+t.src_pseudo_letter+")|(?:"+t.src_pseudo_letter+"(?:-|"+t.src_pseudo_letter+"){0,61}"+t.src_pseudo_letter+"))",t.src_host="(?:(?:(?:(?:"+t.src_domain+")\\.)*"+t.src_domain+"))",t.tpl_host_fuzzy="(?:"+t.src_ip4+"|(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%)))",t.tpl_host_no_ip_fuzzy="(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%))",t.src_host_strict=t.src_host+t.src_host_terminator,t.tpl_host_fuzzy_strict=t.tpl_host_fuzzy+t.src_host_terminator,t.src_host_port_strict=t.src_host+t.src_port+t.src_host_terminator,t.tpl_host_port_fuzzy_strict=t.tpl_host_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_port_no_ip_fuzzy_strict=t.tpl_host_no_ip_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+t.src_ZPCc+"|>|$))",t.tpl_email_fuzzy='(^|[><｜]|"|\\(|'+t.src_ZCc+")("+t.src_email_name+"@"+t.tpl_host_fuzzy_strict+")",t.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_fuzzy_strict+t.src_path+")",t.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_no_ip_fuzzy_strict+t.src_path+")",t}function assign(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){t&&Object.keys(t).forEach((function(r){e[r]=t[r]}))})),e}function _class(e){return Object.prototype.toString.call(e)}function isString(e){return"[object String]"===_class(e)}function isObject(e){return"[object Object]"===_class(e)}function isRegExp(e){return"[object RegExp]"===_class(e)}function isFunction(e){return"[object Function]"===_class(e)}function escapeRE(e){return e.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}ParserInline.prototype.skipToken=function(e){const t=e.pos,r=this.ruler.getRules(""),n=r.length,s=e.md.options.maxNesting,o=e.cache;if(void 0!==o[t])return void(e.pos=o[t]);let i=!1;if(e.level<s){for(let s=0;s<n;s++)if(e.level++,i=r[s](e,!0),e.level--,i){if(t>=e.pos)throw new Error("inline rule didn't increment state.pos");break}}else e.pos=e.posMax;i||e.pos++,o[t]=e.pos},ParserInline.prototype.tokenize=function(e){const t=this.ruler.getRules(""),r=t.length,n=e.posMax,s=e.md.options.maxNesting;for(;e.pos<n;){const o=e.pos;let i=!1;if(e.level<s)for(let n=0;n<r;n++)if(i=t[n](e,!1),i){if(o>=e.pos)throw new Error("inline rule didn't increment state.pos");break}if(i){if(e.pos>=n)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},ParserInline.prototype.parse=function(e,t,r,n){const s=new this.State(e,t,r,n);this.tokenize(s);const o=this.ruler2.getRules(""),i=o.length;for(let e=0;e<i;e++)o[e](s)},ParserInline.prototype.State=StateInline;const defaultOptions={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};function isOptionsObj(e){return Object.keys(e||{}).reduce((function(e,t){return e||defaultOptions.hasOwnProperty(t)}),!1)}const defaultSchemas={"http:":{validate:function(e,t,r){const n=e.slice(t);return r.re.http||(r.re.http=new RegExp("^\\/\\/"+r.re.src_auth+r.re.src_host_port_strict+r.re.src_path,"i")),r.re.http.test(n)?n.match(r.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(e,t,r){const n=e.slice(t);return r.re.no_http||(r.re.no_http=new RegExp("^"+r.re.src_auth+"(?:localhost|(?:(?:"+r.re.src_domain+")\\.)+"+r.re.src_domain_root+")"+r.re.src_port+r.re.src_host_terminator+r.re.src_path,"i")),r.re.no_http.test(n)?t>=3&&":"===e[t-3]||t>=3&&"/"===e[t-3]?0:n.match(r.re.no_http)[0].length:0}},"mailto:":{validate:function(e,t,r){const n=e.slice(t);return r.re.mailto||(r.re.mailto=new RegExp("^"+r.re.src_email_name+"@"+r.re.src_host_strict,"i")),r.re.mailto.test(n)?n.match(r.re.mailto)[0].length:0}}},tlds_2ch_src_re="a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]",tlds_default="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function resetScanCache(e){e.__index__=-1,e.__text_cache__=""}function createValidator(e){return function(t,r){const n=t.slice(r);return e.test(n)?n.match(e)[0].length:0}}function createNormalizer(){return function(e,t){t.normalize(e)}}function compile(e){const t=e.re=reFactory(e.__opts__),r=e.__tlds__.slice();function n(e){return e.replace("%TLDS%",t.src_tlds)}e.onCompile(),e.__tlds_replaced__||r.push(tlds_2ch_src_re),r.push(t.src_xn),t.src_tlds=r.join("|"),t.email_fuzzy=RegExp(n(t.tpl_email_fuzzy),"i"),t.link_fuzzy=RegExp(n(t.tpl_link_fuzzy),"i"),t.link_no_ip_fuzzy=RegExp(n(t.tpl_link_no_ip_fuzzy),"i"),t.host_fuzzy_test=RegExp(n(t.tpl_host_fuzzy_test),"i");const s=[];function o(e,t){throw new Error('(LinkifyIt) Invalid schema "'+e+'": '+t)}e.__compiled__={},Object.keys(e.__schemas__).forEach((function(t){const r=e.__schemas__[t];if(null===r)return;const n={validate:null,link:null};if(e.__compiled__[t]=n,isObject(r))return isRegExp(r.validate)?n.validate=createValidator(r.validate):isFunction(r.validate)?n.validate=r.validate:o(t,r),void(isFunction(r.normalize)?n.normalize=r.normalize:r.normalize?o(t,r):n.normalize=createNormalizer());isString(r)?s.push(t):o(t,r)})),s.forEach((function(t){e.__compiled__[e.__schemas__[t]]&&(e.__compiled__[t].validate=e.__compiled__[e.__schemas__[t]].validate,e.__compiled__[t].normalize=e.__compiled__[e.__schemas__[t]].normalize)})),e.__compiled__[""]={validate:null,normalize:createNormalizer()};const i=Object.keys(e.__compiled__).filter((function(t){return t.length>0&&e.__compiled__[t]})).map(escapeRE).join("|");e.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+i+")","i"),e.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+i+")","ig"),e.re.schema_at_start=RegExp("^"+e.re.schema_search.source,"i"),e.re.pretest=RegExp("("+e.re.schema_test.source+")|("+e.re.host_fuzzy_test.source+")|@","i"),resetScanCache(e)}function Match(e,t){const r=e.__index__,n=e.__last_index__,s=e.__text_cache__.slice(r,n);this.schema=e.__schema__.toLowerCase(),this.index=r+t,this.lastIndex=n+t,this.raw=s,this.text=s,this.url=s}function createMatch(e,t){const r=new Match(e,t);return e.__compiled__[r.schema].normalize(r,e),r}function LinkifyIt(e,t){if(!(this instanceof LinkifyIt))return new LinkifyIt(e,t);t||isOptionsObj(e)&&(t=e,e={}),this.__opts__=assign({},defaultOptions,t),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=assign({},defaultSchemas,e),this.__compiled__={},this.__tlds__=tlds_default,this.__tlds_replaced__=!1,this.re={},compile(this)}LinkifyIt.prototype.add=function(e,t){return this.__schemas__[e]=t,compile(this),this},LinkifyIt.prototype.set=function(e){return this.__opts__=assign(this.__opts__,e),this},LinkifyIt.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;let t,r,n,s,o,i,a,c,l;if(this.re.schema_test.test(e))for(a=this.re.schema_search,a.lastIndex=0;null!==(t=a.exec(e));)if(s=this.testSchemaAt(e,t[2],a.lastIndex),s){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+s;break}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(c=e.search(this.re.host_fuzzy_test),c>=0&&(this.__index__<0||c<this.__index__)&&null!==(r=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))&&(o=r.index+r[1].length,(this.__index__<0||o<this.__index__)&&(this.__schema__="",this.__index__=o,this.__last_index__=r.index+r[0].length))),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&(l=e.indexOf("@"),l>=0&&null!==(n=e.match(this.re.email_fuzzy))&&(o=n.index+n[1].length,i=n.index+n[0].length,(this.__index__<0||o<this.__index__||o===this.__index__&&i>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=o,this.__last_index__=i))),this.__index__>=0},LinkifyIt.prototype.pretest=function(e){return this.re.pretest.test(e)},LinkifyIt.prototype.testSchemaAt=function(e,t,r){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(e,r,this):0},LinkifyIt.prototype.match=function(e){const t=[];let r=0;this.__index__>=0&&this.__text_cache__===e&&(t.push(createMatch(this,r)),r=this.__last_index__);let n=r?e.slice(r):e;for(;this.test(n);)t.push(createMatch(this,r)),n=n.slice(this.__last_index__),r+=this.__last_index__;return t.length?t:null},LinkifyIt.prototype.matchAtStart=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return null;const t=this.re.schema_at_start.exec(e);if(!t)return null;const r=this.testSchemaAt(e,t[2],t[0].length);return r?(this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+r,createMatch(this,0)):null},LinkifyIt.prototype.tlds=function(e,t){return e=Array.isArray(e)?e:[e],t?(this.__tlds__=this.__tlds__.concat(e).sort().filter((function(e,t,r){return e!==r[t-1]})).reverse(),compile(this),this):(this.__tlds__=e.slice(),this.__tlds_replaced__=!0,compile(this),this)},LinkifyIt.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),"mailto:"!==e.schema||/^mailto:/i.test(e.url)||(e.url="mailto:"+e.url)},LinkifyIt.prototype.onCompile=function(){};const maxInt=2147483647,base=36,tMin=1,tMax=26,skew=38,damp=700,initialBias=72,initialN=128,delimiter="-",regexPunycode=/^xn--/,regexNonASCII=/[^\0-\x7F]/,regexSeparators=/[\x2E\u3002\uFF0E\uFF61]/g,errors={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},baseMinusTMin=35,floor=Math.floor,stringFromCharCode=String.fromCharCode;function error(e){throw new RangeError(errors[e])}function map(e,t){const r=[];let n=e.length;for(;n--;)r[n]=t(e[n]);return r}function mapDomain(e,t){const r=e.split("@");let n="";r.length>1&&(n=r[0]+"@",e=r[1]);return n+map((e=e.replace(regexSeparators,".")).split("."),t).join(".")}function ucs2decode(e){const t=[];let r=0;const n=e.length;for(;r<n;){const s=e.charCodeAt(r++);if(s>=55296&&s<=56319&&r<n){const n=e.charCodeAt(r++);56320==(64512&n)?t.push(((1023&s)<<10)+(1023&n)+65536):(t.push(s),r--)}else t.push(s)}return t}const ucs2encode=e=>String.fromCodePoint(...e),basicToDigit=function(e){return e>=48&&e<58?e-48+26:e>=65&&e<91?e-65:e>=97&&e<123?e-97:36},digitToBasic=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},adapt=function(e,t,r){let n=0;for(e=r?floor(e/700):e>>1,e+=floor(e/t);e>455;n+=36)e=floor(e/35);return floor(n+36*e/(e+38))},decode=function(e){const t=[],r=e.length;let n=0,s=128,o=72,i=e.lastIndexOf("-");i<0&&(i=0);for(let r=0;r<i;++r)e.charCodeAt(r)>=128&&error("not-basic"),t.push(e.charCodeAt(r));for(let a=i>0?i+1:0;a<r;){const i=n;for(let t=1,s=36;;s+=36){a>=r&&error("invalid-input");const i=basicToDigit(e.charCodeAt(a++));i>=36&&error("invalid-input"),i>floor((maxInt-n)/t)&&error("overflow"),n+=i*t;const c=s<=o?1:s>=o+26?26:s-o;if(i<c)break;const l=36-c;t>floor(maxInt/l)&&error("overflow"),t*=l}const c=t.length+1;o=adapt(n-i,c,0==i),floor(n/c)>maxInt-s&&error("overflow"),s+=floor(n/c),n%=c,t.splice(n++,0,s)}return String.fromCodePoint(...t)},encode=function(e){const t=[],r=(e=ucs2decode(e)).length;let n=128,s=0,o=72;for(const r of e)r<128&&t.push(stringFromCharCode(r));const i=t.length;let a=i;for(i&&t.push("-");a<r;){let r=maxInt;for(const t of e)t>=n&&t<r&&(r=t);const c=a+1;r-n>floor((maxInt-s)/c)&&error("overflow"),s+=(r-n)*c,n=r;for(const r of e)if(r<n&&++s>maxInt&&error("overflow"),r===n){let e=s;for(let r=36;;r+=36){const n=r<=o?1:r>=o+26?26:r-o;if(e<n)break;const s=e-n,i=36-n;t.push(stringFromCharCode(digitToBasic(n+s%i,0))),e=floor(s/i)}t.push(stringFromCharCode(digitToBasic(e,0))),o=adapt(s,c,a===i),s=0,++a}++s,++n}return t.join("")},toUnicode=function(e){return mapDomain(e,(function(e){return regexPunycode.test(e)?decode(e.slice(4).toLowerCase()):e}))},toASCII=function(e){return mapDomain(e,(function(e){return regexNonASCII.test(e)?"xn--"+encode(e):e}))},punycode={version:"2.3.1",ucs2:{decode:ucs2decode,encode:ucs2encode},decode:decode,encode:encode,toASCII:toASCII,toUnicode:toUnicode},cfg_default={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}},cfg_zero={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","fragments_join"]}}},cfg_commonmark={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","fragments_join"]}}},config={default:cfg_default,zero:cfg_zero,commonmark:cfg_commonmark},BAD_PROTO_RE=/^(vbscript|javascript|file|data):/,GOOD_DATA_RE=/^data:image\/(gif|png|jpeg|webp);/;function validateLink(e){const t=e.trim().toLowerCase();return!BAD_PROTO_RE.test(t)||GOOD_DATA_RE.test(t)}const RECODE_HOSTNAME_FOR=["http:","https:","mailto:"];function normalizeLink(e){const t=urlParse(e,!0);if(t.hostname&&(!t.protocol||RECODE_HOSTNAME_FOR.indexOf(t.protocol)>=0))try{t.hostname=punycode.toASCII(t.hostname)}catch(e){}return encode$1(format(t))}function normalizeLinkText(e){const t=urlParse(e,!0);if(t.hostname&&(!t.protocol||RECODE_HOSTNAME_FOR.indexOf(t.protocol)>=0))try{t.hostname=punycode.toUnicode(t.hostname)}catch(e){}return decode$1(format(t),decode$1.defaultChars+"%")}function MarkdownIt(e,t){if(!(this instanceof MarkdownIt))return new MarkdownIt(e,t);t||isString$1(e)||(t=e||{},e="default"),this.inline=new ParserInline,this.block=new ParserBlock,this.core=new Core,this.renderer=new Renderer,this.linkify=new LinkifyIt,this.validateLink=validateLink,this.normalizeLink=normalizeLink,this.normalizeLinkText=normalizeLinkText,this.utils=utils,this.helpers=assign$1({},helpers),this.options={},this.configure(e),t&&this.set(t)}MarkdownIt.prototype.set=function(e){return assign$1(this.options,e),this},MarkdownIt.prototype.configure=function(e){const t=this;if(isString$1(e)){const t=e;if(!(e=config[t]))throw new Error('Wrong `markdown-it` preset "'+t+'", check name')}if(!e)throw new Error("Wrong `markdown-it` preset, can't be empty");return e.options&&t.set(e.options),e.components&&Object.keys(e.components).forEach((function(r){e.components[r].rules&&t[r].ruler.enableOnly(e.components[r].rules),e.components[r].rules2&&t[r].ruler2.enableOnly(e.components[r].rules2)})),this},MarkdownIt.prototype.enable=function(e,t){let r=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){r=r.concat(this[t].ruler.enable(e,!0))}),this),r=r.concat(this.inline.ruler2.enable(e,!0));const n=e.filter((function(e){return r.indexOf(e)<0}));if(n.length&&!t)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+n);return this},MarkdownIt.prototype.disable=function(e,t){let r=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){r=r.concat(this[t].ruler.disable(e,!0))}),this),r=r.concat(this.inline.ruler2.disable(e,!0));const n=e.filter((function(e){return r.indexOf(e)<0}));if(n.length&&!t)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+n);return this},MarkdownIt.prototype.use=function(e){const t=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,t),this},MarkdownIt.prototype.parse=function(e,t){if("string"!=typeof e)throw new Error("Input data should be a String");const r=new this.core.State(e,this,t);return this.core.process(r),r.tokens},MarkdownIt.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},MarkdownIt.prototype.parseInline=function(e,t){const r=new this.core.State(e,this,t);return r.inlineMode=!0,this.core.process(r),r.tokens},MarkdownIt.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)};const md=new MarkdownIt({html:!0,linkify:!0,typographer:!0,breaks:!1});function renderMarkdown(e){if(!e||"string"!=typeof e)return"";try{return md.render(e)}catch(t){return console.error("Error rendering markdown:",t),e}}md.renderer.rules.link_open=function(e,t,r,n,s){const o=e[t].attrIndex("target");o<0?e[t].attrPush(["target","_blank"]):e[t].attrs[o][1]="_blank";const i=e[t].attrIndex("rel");return i<0?e[t].attrPush(["rel","noopener noreferrer"]):e[t].attrs[i][1]="noopener noreferrer",s.renderToken(e,t,r)};const $$Astro=createAstro("https://infpik.store"),$$slug=createComponent((async(e,t,r)=>{const n=e.createAstro($$Astro,t,r);n.self=$$slug;const{slug:s}=n.params;if(!s)return n.redirect("/products");let o=null;try{const e=n.locals?.runtime?.env,t=createPolarClient(e),r=e?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(r){const e=await t.products.list({organizationId:r,isArchived:!1}),i=(e.result?.items||[]).map(transformPolarProduct).filter((e=>null!==e));if(o=i.find((e=>e.slug===s))||null,!o)return n.redirect("/products")}else;}catch(e){return console.error("Error fetching product:",e),n.redirect("/products")}if(!o||!o.id||!o.name)return n.redirect("/products");let i="";try{const e=n.locals?.runtime?.env,t=createPolarClient(e),r=await t.checkoutLinks.create({paymentProcessor:"stripe",productId:o.id,allowDiscountCodes:!0,requireBillingAddress:!1,successUrl:"http://infpik.store/success"});i=r.url}catch(e){console.error("Error creating checkout URL:",e)}const a=renderMarkdown(o.description),c=`http://infpik.store/products/${o.slug}`,l={name:o.name,description:o.description,images:o.images,price:o.price,currency:o.currency,isAvailable:o.isAvailable,id:o.id,url:c},u={items:[{name:"Home",url:"http://infpik.store"},{name:"Products",url:"http://infpik.store/products"},{name:o.name,url:c}]};return renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:`${o.name} - InfPik`,description:o.description,image:o.images[0]||"/og-image.jpg",canonical:c,type:"product"},{default:async e=>renderTemplate`  ${renderComponent(e,"StructuredData",$$StructuredData,{type:"Product",data:l})} ${renderComponent(e,"StructuredData",$$StructuredData,{type:"BreadcrumbList",data:u})} ${renderComponent(e,"StructuredData",$$StructuredData,{type:"FAQPage",data:{faqs:[{question:"What formats are included with my purchase?",answer:"You'll receive high-resolution JPEG files optimized for web and print use. Additional formats like PNG may be available depending on the specific image."},{question:"Can I use these icons for commercial projects?",answer:"Yes! All our icons come with a commercial license that allows you to use them in both personal and commercial projects without additional fees."},{question:"How do I download my icons after purchase?",answer:"After completing your purchase, you'll receive an email with download links. You can also access your downloads from your account dashboard."},{question:"Are there any usage restrictions?",answer:"Our license allows broad usage rights. You cannot resell or redistribute the icons as-is, but you can use them in your creative projects and commercial work."}]}})} ${maybeRenderHead()}<div class="container max-w-7xl"> <div class="mt-8 mb-8"> <nav class="flex items-center gap-2 text-sm text-primary-600"> <a href="/" class="hover:text-accent-600 transition-colors">Home</a> <span>/</span> <a href="/products" class="hover:text-accent-600 transition-colors">Products</a> <span>/</span> <span class="text-primary-900 font-medium">${o.name}</span> </nav> </div> <div class="grid grid-cols-1 lg:grid-cols-2 gap-12"> <div class="lg:sticky lg:self-start" style="top: calc(var(--header-height, 80px) + 1rem);"> ${renderComponent(e,"ImageGallery",$$ImageGallery,{images:o.images,productName:o.name})} </div> <div class="space-y-8"> <div> <h1 class="text-3xl lg:text-4xl font-bold text-primary-900 mb-6">${o.name}</h1> </div> <div class="prose prose-gray max-w-none"> <h3 class="text-lg font-semibold text-primary-900 mb-3">Description</h3> <div class="text-primary-700 leading-relaxed">${unescapeHTML(a)}</div> </div> ${o.tags&&o.tags.length>0&&renderTemplate`<div> <h3 class="text-lg font-semibold text-gray-900 mb-3">Similar</h3> <div class="flex flex-wrap gap-2"> ${o.tags.map((e=>renderTemplate`<a${addAttribute(`/products/tag/${e}`,"href")} class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200 hover:text-gray-900 transition-colors duration-200 cursor-pointer"> ${e} </a>`))} </div> </div>`} <!-- Trust Signals --> ${renderComponent(e,"TrustSignals",$$TrustSignals,{class:"mb-6"})} <div class="pt-6 border-t border-primary-200"> <div class="flex gap-3"> ${i?renderTemplate`<a${addAttribute(i,"href")} class="flex-1 inline-flex items-center justify-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6"></path> </svg> <span>Buy Now - ${formatPrice(o.price,o.currency)}</span> </a>`:renderTemplate`<button class="flex-1 bg-primary-300 text-primary-600 px-6 py-3 rounded-full font-semibold text-base cursor-not-allowed" disabled>
Checkout Unavailable
</button>`} <button class="inline-flex items-center justify-center gap-2 bg-primary-50 border-2 border-primary-200 text-primary-700 px-6 py-3 rounded-full font-semibold text-base transition-all duration-300 hover:bg-primary-100 hover:border-primary-300 hover:text-primary-900" onclick="navigator.share ? navigator.share({title: document.title, url: window.location.href}) : navigator.clipboard.writeText(window.location.href)"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path> </svg> <span>Share</span> </button> </div> </div> <!-- Product Benefits --> ${renderComponent(e,"ProductBenefits",$$ProductBenefits,{class:"mt-8"})} </div> </div> <!-- Product FAQ --> ${renderComponent(e,"ProductFAQ",$$ProductFAQ,{class:"mt-12"})} <!-- Related Images Section --> ${renderComponent(e,"RelatedImages",$$RelatedImages,{currentProduct:o})} <div class="mt-16 text-center"> <h2 class="text-3xl font-bold text-primary-900 mb-4">You might also like</h2> <p class="text-primary-600 mb-8">Browse our full collection of 3D icons</p> <a href="/products" class="inline-flex items-center gap-2 bg-accent-600 text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:bg-accent-700 hover:shadow-lg hover:-translate-y-0.5">
View All Icons
<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path> </svg> </a> </div> </div> `})} ${renderScript(e,"D:/code/image/polar-image-store/src/pages/products/[slug].astro?astro&type=script&index=0&lang.ts")}`}),"D:/code/image/polar-image-store/src/pages/products/[slug].astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/products/[slug].astro",$$url="/products/[slug]",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$slug,file:$$file,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};