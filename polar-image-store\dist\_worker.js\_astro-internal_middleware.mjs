globalThis.process??={},globalThis.process.env??={};import{d as defineMiddleware,s as sequence}from"./chunks/index_C3Y4RDr1.mjs";import{getOptimalFormat}from"./chunks/imageOptimization_Dac7PwDt.mjs";import"./chunks/astro-designed-error-pages_DkmD2a_0.mjs";import"./chunks/astro/server_BgKLHZ62.mjs";const onRequest$2=defineMiddleware((async(e,t)=>{const{request:s,url:a}=e;if(a.pathname.startsWith("/cdn-cgi/image/")){const e=s.headers.get("user-agent")||"",t=s.headers.get("accept")||"";let o;o=t.includes("image/avif")?"avif":t.includes("image/webp")?"webp":getOptimalFormat(e);const i=a.pathname.split("/");if(i.length>=4){const e=i[3],t=i.slice(4).join("/");if(e.includes("format=auto")){const s=e.replace("format=auto",`format=${o}`),i=new URL(`/cdn-cgi/image/${s}/${t}`,a.origin);return Response.redirect(i.toString(),302)}}}const o=await t(),i=new Headers(o.headers),n=a.pathname;return n.match(/\.(js|css|woff2?|ttf|eot|svg|ico)$/)?i.set("Cache-Control","public, max-age=31536000, immutable"):n.match(/\.(jpg|jpeg|png|webp|avif|gif)$/i)?(i.set("Cache-Control","public, max-age=31536000, immutable"),i.set("Vary","Accept")):n.startsWith("/api/")?i.set("Cache-Control","public, max-age=300, s-maxage=300"):n.startsWith("/products/")?i.set("Cache-Control","public, max-age=1800, s-maxage=1800"):i.set("Cache-Control","public, max-age=300, s-maxage=300"),n.startsWith("/cdn-cgi/image/")&&(i.set("Vary","Accept"),i.set("Accept-CH","Viewport-Width, Width, DPR")),i.set("X-Content-Type-Options","nosniff"),i.set("X-Frame-Options","DENY"),i.set("X-XSS-Protection","1; mode=block"),i.set("Referrer-Policy","strict-origin-when-cross-origin"),"/"===n&&i.set("Link","</fonts/inter.woff2>; rel=preload; as=font; type=font/woff2; crossorigin"),new Response(o.body,{status:o.status,statusText:o.statusText,headers:i})})),onRequest$1=(e,t)=>(e.isPrerendered&&(e.locals.runtime??={env:process.env}),t()),onRequest=sequence(onRequest$1,onRequest$2);export{onRequest};