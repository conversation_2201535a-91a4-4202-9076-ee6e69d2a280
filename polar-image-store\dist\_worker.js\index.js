globalThis.process??={},globalThis.process.env??={};import{renderers}from"./renderers.mjs";import{c as createExports}from"./chunks/server_GGV-eCom.mjs";import{manifest}from"./manifest_DQiz-ajq.mjs";const serverIslandMap=new Map,_page0=()=>import("./pages/_image.astro.mjs"),_page1=()=>import("./pages/404.astro.mjs"),_page2=()=>import("./pages/about.astro.mjs"),_page3=()=>import("./pages/api/checkout.astro.mjs"),_page4=()=>import("./pages/api/image-proxy.astro.mjs"),_page5=()=>import("./pages/api/portal-redirect.astro.mjs"),_page6=()=>import("./pages/api/products.astro.mjs"),_page7=()=>import("./pages/api/search.astro.mjs"),_page8=()=>import("./pages/api/tags.astro.mjs"),_page9=()=>import("./pages/api/webhooks.astro.mjs"),_page10=()=>import("./pages/privacy.astro.mjs"),_page11=()=>import("./pages/products/category/_category_.astro.mjs"),_page12=()=>import("./pages/products/tag/_tag_.astro.mjs"),_page13=()=>import("./pages/products/_slug_.astro.mjs"),_page14=()=>import("./pages/products.astro.mjs"),_page15=()=>import("./pages/sitemap.xml.astro.mjs"),_page16=()=>import("./pages/success.astro.mjs"),_page17=()=>import("./pages/terms.astro.mjs"),_page18=()=>import("./pages/trending.astro.mjs"),_page19=()=>import("./pages/index.astro.mjs"),pageMap=new Map([["node_modules/astro/dist/assets/endpoint/generic.js",_page0],["src/pages/404.astro",_page1],["src/pages/about.astro",_page2],["src/pages/api/checkout.ts",_page3],["src/pages/api/image-proxy.ts",_page4],["src/pages/api/portal-redirect.ts",_page5],["src/pages/api/products.ts",_page6],["src/pages/api/search.ts",_page7],["src/pages/api/tags.ts",_page8],["src/pages/api/webhooks.ts",_page9],["src/pages/privacy.astro",_page10],["src/pages/products/category/[category].astro",_page11],["src/pages/products/tag/[tag].astro",_page12],["src/pages/products/[slug].astro",_page13],["src/pages/products/index.astro",_page14],["src/pages/sitemap.xml.ts",_page15],["src/pages/success.astro",_page16],["src/pages/terms.astro",_page17],["src/pages/trending.astro",_page18],["src/pages/index.astro",_page19]]),_manifest=Object.assign(manifest,{pageMap:pageMap,serverIslandMap:serverIslandMap,renderers:renderers,actions:()=>import("./_noop-actions.mjs"),middleware:()=>import("./_astro-internal_middleware.mjs")}),_exports=createExports(_manifest),__astrojsSsrVirtualEntry=_exports.default;export{__astrojsSsrVirtualEntry as default,pageMap};