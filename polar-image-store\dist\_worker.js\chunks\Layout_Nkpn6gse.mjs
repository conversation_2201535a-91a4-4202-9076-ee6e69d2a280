globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,b as addAttribute,d as renderTemplate,s as spreadAttributes,u as unescapeHTML,r as renderComponent,m as maybeRenderHead,e as renderScript,h as renderHead,f as renderSlot}from"./astro/server_BgKLHZ62.mjs";import{$ as $$Image}from"./_astro_assets_BdkCObdS.mjs";const $$Astro$8=createAstro("https://infpik.store"),$$OpenGraphArticleTags=createComponent(((e,t,r)=>{const a=e.createAstro($$Astro$8,t,r);a.self=$$OpenGraphArticleTags;const{publishedTime:o,modifiedTime:s,expirationTime:i,authors:n,section:d,tags:l}=a.props.openGraph.article;return renderTemplate`${o?renderTemplate`<meta property="article:published_time"${addAttribute(o,"content")}>`:null}${s?renderTemplate`<meta property="article:modified_time"${addAttribute(s,"content")}>`:null}${i?renderTemplate`<meta property="article:expiration_time"${addAttribute(i,"content")}>`:null}${n?n.map((e=>renderTemplate`<meta property="article:author"${addAttribute(e,"content")}>`)):null}${d?renderTemplate`<meta property="article:section"${addAttribute(d,"content")}>`:null}${l?l.map((e=>renderTemplate`<meta property="article:tag"${addAttribute(e,"content")}>`)):null}`}),"D:/code/image/polar-image-store/node_modules/astro-seo/src/components/OpenGraphArticleTags.astro",void 0),$$Astro$7=createAstro("https://infpik.store"),$$OpenGraphBasicTags=createComponent(((e,t,r)=>{const a=e.createAstro($$Astro$7,t,r);a.self=$$OpenGraphBasicTags;const{openGraph:o}=a.props;return renderTemplate`<meta property="og:title"${addAttribute(o.basic.title,"content")}><meta property="og:type"${addAttribute(o.basic.type,"content")}><meta property="og:image"${addAttribute(o.basic.image,"content")}><meta property="og:url"${addAttribute(o.basic.url||a.url.href,"content")}>`}),"D:/code/image/polar-image-store/node_modules/astro-seo/src/components/OpenGraphBasicTags.astro",void 0),$$Astro$6=createAstro("https://infpik.store"),$$OpenGraphImageTags=createComponent(((e,t,r)=>{const a=e.createAstro($$Astro$6,t,r);a.self=$$OpenGraphImageTags;const{image:o}=a.props.openGraph.basic,{secureUrl:s,type:i,width:n,height:d,alt:l}=a.props.openGraph.image;return renderTemplate`<meta property="og:image:url"${addAttribute(o,"content")}>${s?renderTemplate`<meta property="og:image:secure_url"${addAttribute(s,"content")}>`:null}${i?renderTemplate`<meta property="og:image:type"${addAttribute(i,"content")}>`:null}${n?renderTemplate`<meta property="og:image:width"${addAttribute(n,"content")}>`:null}${d?renderTemplate`<meta property="og:image:height"${addAttribute(d,"content")}>`:null}${l?renderTemplate`<meta property="og:image:alt"${addAttribute(l,"content")}>`:null}`}),"D:/code/image/polar-image-store/node_modules/astro-seo/src/components/OpenGraphImageTags.astro",void 0),$$Astro$5=createAstro("https://infpik.store"),$$OpenGraphOptionalTags=createComponent(((e,t,r)=>{const a=e.createAstro($$Astro$5,t,r);a.self=$$OpenGraphOptionalTags;const{optional:o}=a.props.openGraph;return renderTemplate`${o.audio?renderTemplate`<meta property="og:audio"${addAttribute(o.audio,"content")}>`:null}${o.description?renderTemplate`<meta property="og:description"${addAttribute(o.description,"content")}>`:null}${o.determiner?renderTemplate`<meta property="og:determiner"${addAttribute(o.determiner,"content")}>`:null}${o.locale?renderTemplate`<meta property="og:locale"${addAttribute(o.locale,"content")}>`:null}${o.localeAlternate?.map((e=>renderTemplate`<meta property="og:locale:alternate"${addAttribute(e,"content")}>`))}${o.siteName?renderTemplate`<meta property="og:site_name"${addAttribute(o.siteName,"content")}>`:null}${o.video?renderTemplate`<meta property="og:video"${addAttribute(o.video,"content")}>`:null}`}),"D:/code/image/polar-image-store/node_modules/astro-seo/src/components/OpenGraphOptionalTags.astro",void 0),$$Astro$4=createAstro("https://infpik.store"),$$ExtendedTags=createComponent(((e,t,r)=>{const a=e.createAstro($$Astro$4,t,r);a.self=$$ExtendedTags;const{props:o}=a;return renderTemplate`${o.extend.link?.map((e=>renderTemplate`<link${spreadAttributes(e)}>`))}${o.extend.meta?.map((({content:e,httpEquiv:t,media:r,name:a,property:o})=>renderTemplate`<meta${addAttribute(a,"name")}${addAttribute(o,"property")}${addAttribute(e,"content")}${addAttribute(t,"http-equiv")}${addAttribute(r,"media")}>`))}`}),"D:/code/image/polar-image-store/node_modules/astro-seo/src/components/ExtendedTags.astro",void 0),$$Astro$3=createAstro("https://infpik.store"),$$TwitterTags=createComponent(((e,t,r)=>{const a=e.createAstro($$Astro$3,t,r);a.self=$$TwitterTags;const{card:o,site:s,title:i,creator:n,description:d,image:l,imageAlt:p}=a.props.twitter;return renderTemplate`${o?renderTemplate`<meta name="twitter:card"${addAttribute(o,"content")}>`:null}${s?renderTemplate`<meta name="twitter:site"${addAttribute(s,"content")}>`:null}${i?renderTemplate`<meta name="twitter:title"${addAttribute(i,"content")}>`:null}${l?renderTemplate`<meta name="twitter:image"${addAttribute(l,"content")}>`:null}${p?renderTemplate`<meta name="twitter:image:alt"${addAttribute(p,"content")}>`:null}${d?renderTemplate`<meta name="twitter:description"${addAttribute(d,"content")}>`:null}${n?renderTemplate`<meta name="twitter:creator"${addAttribute(n,"content")}>`:null}`}),"D:/code/image/polar-image-store/node_modules/astro-seo/src/components/TwitterTags.astro",void 0),$$Astro$2=createAstro("https://infpik.store"),$$LanguageAlternatesTags=createComponent(((e,t,r)=>{const a=e.createAstro($$Astro$2,t,r);a.self=$$LanguageAlternatesTags;const{languageAlternates:o}=a.props;return renderTemplate`${o.map((e=>renderTemplate`<link rel="alternate"${addAttribute(e.hrefLang,"hreflang")}${addAttribute(e.href,"href")}>`))}`}),"D:/code/image/polar-image-store/node_modules/astro-seo/src/components/LanguageAlternatesTags.astro",void 0),$$Astro$1=createAstro("https://infpik.store"),$$SEO=createComponent(((e,t,r)=>{const a=e.createAstro($$Astro$1,t,r);a.self=$$SEO,a.props.surpressWarnings=!0,function(e){if(e.openGraph&&(!e.openGraph.basic||null==(e.openGraph.basic.title??void 0)||null==(e.openGraph.basic.type??void 0)||null==(e.openGraph.basic.image??void 0)))throw new Error("If you pass the openGraph prop, you have to at least define the title, type, and image basic properties!");e.title&&e.openGraph?.basic.title&&(e.title!=e.openGraph.basic.title||e.surpressWarnings||console.warn("WARNING(astro-seo): You passed the same value to `title` and `openGraph.optional.title`. This is most likely not what you want. See docs for more.")),!e.openGraph?.basic?.image||e.openGraph?.image?.alt||e.surpressWarnings||console.warn("WARNING(astro-seo): You defined `openGraph.basic.image`, but didn't define `openGraph.image.alt`. This is strongly discouraged.'")}(a.props);let o="";a.props.title?(o=a.props.title,a.props.titleTemplate&&(o=a.props.titleTemplate.replace(/%s/g,o))):a.props.titleDefault&&(o=a.props.titleDefault);const s=a.site??a.url,i=new URL(a.url.pathname+a.url.search,s);return renderTemplate`${o?renderTemplate`<title>${unescapeHTML(o)}</title>`:null}${a.props.charset?renderTemplate`<meta${addAttribute(a.props.charset,"charset")}>`:null}<link rel="canonical"${addAttribute(a.props.canonical||i.href,"href")}>${a.props.description?renderTemplate`<meta name="description"${addAttribute(a.props.description,"content")}>`:null}<meta name="robots"${addAttribute(`${a.props.noindex?"noindex":"index"}, ${a.props.nofollow?"nofollow":"follow"}`,"content")}>${a.props.openGraph&&renderTemplate`${renderComponent(e,"OpenGraphBasicTags",$$OpenGraphBasicTags,{...a.props})}`}${a.props.openGraph?.optional&&renderTemplate`${renderComponent(e,"OpenGraphOptionalTags",$$OpenGraphOptionalTags,{...a.props})}`}${a.props.openGraph?.image&&renderTemplate`${renderComponent(e,"OpenGraphImageTags",$$OpenGraphImageTags,{...a.props})}`}${a.props.openGraph?.article&&renderTemplate`${renderComponent(e,"OpenGraphArticleTags",$$OpenGraphArticleTags,{...a.props})}`}${a.props.twitter&&renderTemplate`${renderComponent(e,"TwitterTags",$$TwitterTags,{...a.props})}`}${a.props.extend&&renderTemplate`${renderComponent(e,"ExtendedTags",$$ExtendedTags,{...a.props})}`}${a.props.languageAlternates&&renderTemplate`${renderComponent(e,"LanguageAlternatesTags",$$LanguageAlternatesTags,{...a.props})}`}`}),"D:/code/image/polar-image-store/node_modules/astro-seo/src/SEO.astro",void 0),$$SearchModal=createComponent((async(e,t,r)=>renderTemplate`<!-- Search Modal - Hidden by default -->${maybeRenderHead()}<div id="searchModal" class="fixed inset-0 z-[9999] hidden" role="dialog" aria-modal="true" aria-labelledby="searchModalTitle" data-astro-cid-qk3db3zz> <!-- Backdrop --> <div id="searchModalBackdrop" class="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300 opacity-0" aria-hidden="true" data-astro-cid-qk3db3zz></div> <!-- Modal Content --> <div id="searchModalContent" class="fixed inset-0 bg-white shadow-2xl transform translate-x-full transition-transform duration-300 ease-out flex flex-col" data-astro-cid-qk3db3zz> <!-- Header with back button and search input --> <div class="sticky top-0 z-50 bg-white border-b border-gray-200" data-astro-cid-qk3db3zz> <div class="flex items-center px-4 py-3" data-astro-cid-qk3db3zz> <button id="searchModalBackButton" class="mr-3 p-2 -ml-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full transition-colors" aria-label="Close search" data-astro-cid-qk3db3zz> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-qk3db3zz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" data-astro-cid-qk3db3zz></path> </svg> </button> <!-- Search Input --> <div class="flex-1 relative" data-astro-cid-qk3db3zz> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" data-astro-cid-qk3db3zz> <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-qk3db3zz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" data-astro-cid-qk3db3zz></path> </svg> </div> <input type="text" id="searchModalInput" placeholder="Search for icons..." class="block w-full pl-10 pr-4 py-3 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-lg" autocomplete="off" data-astro-cid-qk3db3zz> <!-- Clear button --> <button id="searchModalClearButton" class="absolute inset-y-0 right-0 pr-3 flex items-center text-primary-400 hover:text-primary-600 transition-colors duration-200" style="display: none;" aria-label="Clear search" data-astro-cid-qk3db3zz> <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-qk3db3zz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" data-astro-cid-qk3db3zz></path> </svg> </button> </div> </div> </div> <!-- Scrollable Content --> <div class="flex-1 overflow-y-auto px-4 py-6" data-astro-cid-qk3db3zz> <!-- Search Results Container --> <div id="searchModalResults" class="hidden" data-astro-cid-qk3db3zz> <div id="searchModalResultsHeader" class="mb-6" data-astro-cid-qk3db3zz> <p id="searchModalResultsText" class="text-gray-600" data-astro-cid-qk3db3zz></p> </div> <!-- Error State --> <div id="searchModalError" class="hidden bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden" data-astro-cid-qk3db3zz> <div class="p-4 text-center text-red-600" data-astro-cid-qk3db3zz> <div id="searchModalErrorText" class="text-sm" data-astro-cid-qk3db3zz></div> </div> </div> <!-- Results List --> <div id="searchModalResultsList" class="hidden bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden" data-astro-cid-qk3db3zz> <!-- Header Section --> <div id="searchModalResultsListHeader" class="text-xs text-primary-500 px-3 py-2 border-b border-primary-100 bg-gray-50" data-astro-cid-qk3db3zz></div> <!-- Results Items --> <div id="searchModalResultsItems" class="p-2" data-astro-cid-qk3db3zz> <!-- Results will be populated here --> </div> <!-- Footer Section --> <div id="searchModalResultsFooter" class="hidden p-3 border-t border-primary-100 bg-gray-50" data-astro-cid-qk3db3zz> <div class="text-center text-primary-600 text-sm" data-astro-cid-qk3db3zz> <span id="searchModalResultsCount" data-astro-cid-qk3db3zz></span> </div> </div> </div> <!-- No Results --> <div id="searchModalNoResults" class="hidden bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden" data-astro-cid-qk3db3zz> <div class="p-4 text-center" data-astro-cid-qk3db3zz> <div id="searchModalNoResultsText" class="text-primary-600 mb-2" data-astro-cid-qk3db3zz></div> <a href="/products" class="text-accent-600 hover:text-accent-700 font-medium text-sm transition-colors" data-astro-cid-qk3db3zz>
Browse all products →
</a> </div> <!-- Popular Search Suggestions --> <div id="searchModalSuggestions" class="hidden border-t border-primary-100 p-4" data-astro-cid-qk3db3zz> <p class="text-sm font-medium text-primary-900 mb-3 text-center" data-astro-cid-qk3db3zz>Try these searches:</p> <div id="searchModalSuggestionsContainer" class="flex flex-wrap gap-2 justify-center" data-astro-cid-qk3db3zz> <!-- Suggestions will be populated here --> </div> </div> </div> </div> <!-- Initial Search State --> <div id="searchModalInitialState" data-astro-cid-qk3db3zz> <!-- Recent Searches --> <div id="searchModalRecentSearches" class="mb-8" style="display: none;" data-astro-cid-qk3db3zz> <div class="flex items-center justify-between mb-4" data-astro-cid-qk3db3zz> <h2 class="text-lg font-semibold text-primary-900" data-astro-cid-qk3db3zz>Recent Searches</h2> <button id="searchModalClearRecentSearches" class="p-2 text-primary-400 hover:text-primary-600 hover:bg-primary-50 rounded-full transition-all duration-200" title="Clear recent searches" data-astro-cid-qk3db3zz> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-qk3db3zz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" data-astro-cid-qk3db3zz></path> </svg> </button> </div> <div id="searchModalRecentSearchesList" class="bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden" data-astro-cid-qk3db3zz> <!-- Recent searches will be populated here --> </div> </div> <!-- Popular Tags --> <div id="searchModalPopularTags" class="mb-8" data-astro-cid-qk3db3zz> <h2 class="text-lg font-semibold text-primary-900 mb-4" data-astro-cid-qk3db3zz>Popular Searches</h2> <div id="searchModalPopularTagsList" class="bg-white rounded-xl border border-primary-200 shadow-lg overflow-hidden" data-astro-cid-qk3db3zz> <div class="p-2" id="searchModalPopularTagsContainer" data-astro-cid-qk3db3zz> <!-- Popular tags will be populated here --> </div> </div> </div> <!-- Quick Actions --> <div data-astro-cid-qk3db3zz> <h2 class="text-lg font-semibold text-gray-900 mb-4" data-astro-cid-qk3db3zz>Browse</h2> <div class="space-y-3" data-astro-cid-qk3db3zz> <a href="/products" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors" data-astro-cid-qk3db3zz> <span class="font-medium text-gray-900" data-astro-cid-qk3db3zz>All Products</span> <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-qk3db3zz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-astro-cid-qk3db3zz></path> </svg> </a> <a href="/trending" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors" data-astro-cid-qk3db3zz> <span class="font-medium text-gray-900" data-astro-cid-qk3db3zz>Trending Now</span> <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-astro-cid-qk3db3zz> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" data-astro-cid-qk3db3zz></path> </svg> </a> </div> </div> </div> </div> </div> </div>  ${renderScript(e,"D:/code/image/polar-image-store/src/components/SearchModal.astro?astro&type=script&index=0&lang.ts")}`),"D:/code/image/polar-image-store/src/components/SearchModal.astro",void 0),$$Astro=createAstro("https://infpik.store"),$$Layout=createComponent((async(e,t,r)=>{const a=e.createAstro($$Astro,t,r);a.self=$$Layout;const{title:o,description:s="Premium 3D icons for creative projects (PNG, PSD).",image:i="/og-image.jpg",canonical:n,noindex:d=!1,type:l="website"}=a.props,p="http://infpik.store".replace("://www.","://"),c=i.startsWith("http")?i:`${p}${i}`,m=n||`${p}${a.url.pathname}`;return renderTemplate`<html lang="en"> <head>${renderComponent(e,"SEO",$$SEO,{title:o,description:s,canonical:m,noindex:d,charset:"UTF-8",openGraph:{basic:{title:o,type:l,image:c,url:m},optional:{description:s,siteName:"InfPik",locale:"en_US"}},twitter:{card:"summary_large_image",site:"@polarimagestore",creator:"@polarimagestore",title:o,description:s,image:c,imageAlt:`${o} - InfPik`},extend:{link:[{rel:"icon",type:"image/svg+xml",href:"/favicon.svg"},{rel:"sitemap",href:"/sitemap.xml"},{rel:"manifest",href:"/manifest.json"},{rel:"preconnect",href:"https://fonts.googleapis.com"},{rel:"preconnect",href:"https://fonts.gstatic.com",crossorigin:""},{rel:"dns-prefetch",href:"https://polar.sh"},{rel:"dns-prefetch",href:"https://amazonaws.com"},{rel:"dns-prefetch",href:"https://s3.amazonaws.com"},{rel:"preload",href:"/fonts/inter.woff2",as:"font",type:"font/woff2",crossorigin:""}],meta:[{name:"viewport",content:"width=device-width, initial-scale=1.0"},{name:"generator",content:a.generator},{name:"robots",content:d?"noindex, nofollow":"index, follow"},{name:"googlebot",content:d?"noindex, nofollow":"index, follow"},{name:"theme-color",content:"#6366f1"},{name:"msapplication-TileColor",content:"#6366f1"}]}})}${renderHead()}</head> <body class="min-h-screen flex flex-col"> <header class="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b border-primary-100 py-4"> <div class="container"> <nav class="flex items-center justify-between"> <!-- Logo --> <a href="/" class="flex items-center gap-2 text-xl font-bold text-primary-900 hover:text-accent-600 transition-colors"> ${renderComponent(e,"Image",$$Image,{src:"/logo.svg",alt:"Logo",width:32,height:32,class:"w-8 h-8 text-accent-600"})} <span class="hidden md:inline">InfPik</span> </a> <!-- Mobile Search Bar --> <div id="mobileHeaderSearchContainer" class="md:hidden relative flex-1 mx-2 opacity-0 transform -translate-y-2 transition-all duration-300 ease-in-out"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-4 h-4 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="mobileProductSearch" class="block w-full pl-8 pr-4 py-1.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200 text-sm" placeholder="Search for icons..." autocomplete="off"> </div> <!-- Search Bar (Desktop) --> <div id="headerSearchContainer" class="hidden md:flex flex-1 max-w-md mx-8 opacity-0 transform -translate-y-2 transition-all duration-300 ease-in-out"> <div class="relative w-full"> <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"> <svg class="w-5 h-5 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path> </svg> </div> <input type="text" id="productSearch" class="block w-full pl-10 pr-4 py-2.5 border border-primary-200 rounded-full bg-primary-50/50 text-primary-900 placeholder-primary-500 focus:outline-none focus:ring-2 focus:ring-accent-500 focus:border-accent-500 transition-all duration-200" placeholder="Search for icons..." autocomplete="off"> <!-- Search results dropdown (hidden by default) --> <div id="searchResults" class="absolute top-full left-0 right-0 mt-1 bg-white border border-primary-200 rounded-xl shadow-lg z-50 hidden max-h-96 overflow-y-auto"> <!-- Search results will be populated here --> </div> </div> </div> <!-- CTA Button & Mobile Menu --> <div class="flex items-center gap-4"> <a href="/api/portal-redirect" class="btn-secondary hidden md:inline-flex">
My Orders
</a> <a href="/products" class="btn-primary hidden md:inline-flex">
Browse Collection
</a> <a href="/trending" class="btn-secondary hidden md:inline-flex">
Trending Now
</a> <!-- Mobile menu button --> <button class="md:hidden p-2 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full transition-all" id="mobile-menu-button"> <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path> </svg> </button> </div> </nav> <!-- Mobile menu --> <div class="md:hidden hidden" id="mobile-menu"> <div class="pt-4 pb-2 border-t border-primary-100 mt-4"> <!-- Mobile Navigation --> <ul class="space-y-2"> <li><a href="/" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Home</a></li> <li><a href="/products" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Products</a></li> <li><a href="/trending" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">Trending</a></li> <li><a href="/api/portal-redirect" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">My Orders</a></li> <li><a href="/about" class="block py-3 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all">About</a></li> </ul> <!-- Legal Links --> <div class="mt-4 pt-4 border-t border-primary-100"> <p class="px-4 text-xs uppercase text-primary-500 font-medium mb-2">Legal</p> <ul class="space-y-2"> <li><a href="/privacy" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Privacy Policy</a></li> <li><a href="/terms" class="block py-2 px-4 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded-full font-medium transition-all text-sm">Terms of Service</a></li> </ul> </div> <div class="mt-4 pt-4 border-t border-primary-100"> <a href="/products" class="btn-primary w-full justify-center">
Browse Collection
</a> <a href="/trending" class="btn-secondary w-full justify-center mt-2">
Trending Now
</a> </div> </div> </div> </div> </header> <main class="flex-1 pb-12"> ${renderSlot(e,r.default)} </main> <footer class="bg-white border-t border-primary-100 py-12 text-primary-600"> <div class="container"> <div class="text-center"> <div class="flex items-center justify-center gap-2 text-lg font-semibold text-primary-900 mb-4"> ${renderComponent(e,"Image",$$Image,{src:"/logo.svg",alt:"Logo",width:24,height:24,class:"w-6 h-6 text-accent-600"})}
InfPik
</div> <div class="flex justify-center gap-4 mb-4"> <a href="/about" class="text-sm hover:text-accent-600 transition-colors">About Us</a> <a href="/api/portal-redirect" class="text-sm hover:text-accent-600 transition-colors">Order History</a> <a href="/privacy" class="text-sm hover:text-accent-600 transition-colors">Privacy Policy</a> <a href="/terms" class="text-sm hover:text-accent-600 transition-colors">Terms of Service</a> </div> <p class="text-sm">&copy; 2025 InfPik. All rights reserved.</p> </div> </div> </footer> <!-- Search Modal for Mobile --> ${renderComponent(e,"SearchModal",$$SearchModal,{})} ${renderScript(e,"D:/code/image/polar-image-store/src/layouts/Layout.astro?astro&type=script&index=0&lang.ts")}</body></html>`}),"D:/code/image/polar-image-store/src/layouts/Layout.astro",void 0);export{$$Layout as $};