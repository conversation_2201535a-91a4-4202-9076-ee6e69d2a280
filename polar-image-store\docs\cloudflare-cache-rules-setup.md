# Cloudflare Cache Rules Setup cho Image Optimization

## ✅ **MIDDLEWARE CACHE CONFLICT - RESOLVED**

**Vấn đề đã được fix:** Middleware conflict giữa `src/middleware.ts` và `src/middleware/` đã được giải quyết bằng cách merge thành unified middleware với comprehensive cache strategy.

**Kết quả:**
- ✅ HTML pages có proper cache headers (không cần tab ẩn danh)
- ✅ Cloudflare Image Transform hoạt động bình thường
- ✅ Format negotiation AVIF → WebP → JPEG
- ✅ Tất cả content types có cache headers phù hợp

## 🎯 Recommended Cache Rules cho dự án

### Rule 1: Aggressive Cache cho Transformed Images
```
Name: Cache Transformed Images
Priority: 1

When incoming requests match:
  URI Path starts with "/cdn-cgi/image/"

Then:
  Cache eligibility: Eligible for cache
  Edge TTL: Ignore cache-control header and use this TTL
    Duration: 30 days
  Browser TTL: Override origin and use this TTL  
    Duration: 7 days
```

**Lý do**: Transformed images là static, không thay đổi. Aggressive cache giảm origin requests và chi phí.

### Rule 2: Smart Cache cho Original Images
```
Name: Cache Original Images  
Priority: 2

When incoming requests match:
  Hostname contains "amazonaws.com" 
  AND File extension is in {"jpg", "jpeg", "png", "webp", "avif", "gif"}

Then:
  Cache eligibility: Eligible for cache
  Edge TTL: Use cache-control header if present, cache with default TTL if not
  Browser TTL: Override origin and use this TTL
    Duration: 1 day
```

**Lý do**: S3 thường có Cache-Control headers tốt. Fallback to default nếu không có.

### Rule 3: Cache Static Assets
```
Name: Cache Static Assets
Priority: 3

When incoming requests match:
  File extension is in {"css", "js", "woff", "woff2", "svg", "ico"}

Then:
  Cache eligibility: Eligible for cache  
  Edge TTL: Ignore cache-control header and use this TTL
    Duration: 1 year
  Browser TTL: Override origin and use this TTL
    Duration: 30 days
```

## 📊 Performance Impact

### Option 1: "Bypass if no cache-control"
- ❌ Nhiều requests đến origin
- ❌ Slower loading times
- ❌ Higher bandwidth costs
- ✅ Fresh content guaranteed

### Option 2: "Default TTL if no cache-control" 
- ✅ Balanced caching
- ✅ Reasonable performance
- ⚠️ Medium origin requests
- ✅ Good for mixed content

### Option 3: "Ignore cache-control, use custom TTL"
- ✅ Maximum performance
- ✅ Minimum origin requests  
- ✅ Lowest costs
- ⚠️ Potential stale content (không quan trọng cho images)

## 🎯 Specific Recommendations

### Cho Images từ Polar.sh:
**Dùng Option 3** - "Ignore cache-control header and use this TTL"
- TTL: 30 days cho transformed images
- TTL: 7 days cho original images
- Lý do: Images ít khi thay đổi, aggressive cache tốt nhất

### Cho Dynamic Content:
**Dùng Option 2** - "Use cache-control if present, default TTL if not"
- Cho API responses
- Cho user-specific content

### Cho Static Assets:
**Dùng Option 3** - "Ignore cache-control header and use this TTL"  
- TTL: 1 year
- Cho CSS, JS, fonts

## 💰 Cost Optimization

### Image Resizing Costs:
- Mỗi unique transformation: $1/1000 requests
- Cache hit ratio 95%+ → Giảm 95% costs
- VD: 10,000 transformations → chỉ 500 origin requests

### Bandwidth Savings:
- Edge cache → 90% bandwidth reduction
- Browser cache → 50% repeat visitor improvement

## 🔧 Implementation Steps

1. **Dashboard → Rules → Cache Rules**
2. **Create Rule theo thứ tự priority**
3. **Test với browser DevTools**
4. **Monitor Analytics → Caching**
5. **Adjust TTL based on hit ratios**

## 📈 Monitoring

### Key Metrics:
- Cache Hit Ratio: Target >95% cho images
- Origin Requests: Should decrease significantly  
- Image Resizing Usage: Monitor monthly limits
- Page Load Times: Should improve 30-50%

### Alerts Setup:
- Cache hit ratio < 90%
- Image resizing usage > 80% of limit
- Origin error rate > 1%

## 🔧 **UNIFIED MIDDLEWARE ARCHITECTURE**

### **New Cache Strategy (Implemented)**

```typescript
// src/middleware.ts - Unified middleware
export const onRequest = defineMiddleware(async (context, next) => {
  // 1. Image format negotiation (BEFORE processing)
  // 2. Response processing
  // 3. Cache headers based on content type
  // 4. Security & performance headers
});
```

### **Cache Headers by Content Type:**

1. **Static Assets** (CSS, JS, fonts, icons)
   - `Cache-Control: public, max-age=31536000, immutable`
   - 1 year cache với immutable flag

2. **Images** (including transformed)
   - `Cache-Control: public, max-age=31536000, immutable`
   - `Vary: Accept` (cho format negotiation)
   - 1 year cache với format support

3. **API Routes**
   - `Cache-Control: public, max-age=300, s-maxage=300`
   - 5 minutes cache cho dynamic data

4. **Product Pages**
   - `Cache-Control: public, max-age=1800, s-maxage=1800`
   - 30 minutes cache cho semi-static content

5. **Other Pages** (homepage, category pages)
   - `Cache-Control: public, max-age=300, s-maxage=300`
   - 5 minutes cache cho fresh content

### **Security Headers (All Responses):**
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`

### **Image Optimization Headers:**
- `Vary: Accept` (cho Cloudflare Image Transform)
- `Accept-CH: Viewport-Width, Width, DPR` (cho responsive images)

## 🚀 **PERFORMANCE IMPROVEMENTS**

### **Before Fix:**
- ❌ HTML pages: No cache headers → Browser heuristic caching
- ❌ Cần mở tab ẩn danh để thấy changes
- ❌ Middleware conflict → Inconsistent behavior

### **After Fix:**
- ✅ HTML pages: Proper cache headers (5-30 minutes)
- ✅ Không cần tab ẩn danh
- ✅ Unified middleware → Consistent behavior
- ✅ Cloudflare Image Transform hoạt động bình thường
